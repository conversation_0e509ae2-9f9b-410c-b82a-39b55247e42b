"use client";

import RequestIsLoading from "@/components/RequestIsLoading";
import { cn } from "@/lib/utils";
import { useRegisterUser } from "@/store/slices/auth";
import { GoogleOauthLoginSlice } from "@/store/slices/signin/googleOauthLoginSlice";
import { SignUpSchema, SignUpType } from "@/types/signup";
import { handleApiErrors } from "@/utils/general";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import React, { useEffect } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import useUserStore from "../../store/useUserStore";
import useCustomToast from "../CustomToast";
import VerifyEmail from "../Onboarding/VerifyEmail";
import { LoaderButton } from "../ui-extended/loader-button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import SignInWithMicrosoft from "./azure/SignInWithMicrosoft";
import SigninWIthGoogle from "./google/SigninWIthGoogle";
import { useRouter } from "next/navigation";
import InputIcon from "../ui-extended/input-icon";
import { LuEyeOff, LuEye } from "react-icons/lu";
import Checkbox from "../ui-extended/checkbox";

const SignUpCard: React.FC = () => {
	const {
		register,
		handleSubmit,
		setError,
		watch,
		getValues,
		setValue,
		formState: { errors },
	} = useForm<SignUpType>({
		resolver: zodResolver(SignUpSchema),
	});
	// const [showVerifyEmailModal, setShowVerifyEmailModal] =
	// 	React.useState(false);
	const [passwordType, setPasswordType] = React.useState("password");
	const [passwordConfirmationType, setPasswordConfirmationType] =
		React.useState("password");
	const customToast = useCustomToast();
	const registerUserMutaion = useRegisterUser();
	const googleOauthRegisterMutation = GoogleOauthLoginSlice();
	const router = useRouter();

	const reset = useUserStore((s) => s.reset);

	const onSubmit: SubmitHandler<SignUpType> = async (data) => {
		registerUserMutaion.mutate(data, {
			onSuccess: () => {
				customToast("Account created successfully 🎉", {
					id: "signup",
				});
				setTimeout(() => {
					router.push("/sign-in");
				}, 500);
				// setShowVerifyEmailModal(true);
			},
			onError: (error) => {
				if (error.response?.data) {
					handleApiErrors(error.response.data.errors, setError);
					customToast("Password reset failed", {
						id: "reset-password",
						type: "error",
					});
				}
			},
		});
	};

	useEffect(() => {
		reset();
	}, []);
	// console.log(errors);
	return (
		<>
			<form
				className="relative z-10 flex w-full max-w-[488px] flex-col space-y-4 rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
				onSubmit={handleSubmit(onSubmit)}
			>
				<div className="flex flex-col space-y-2 px-4 pb-6 md:px-8">
					<h2 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
						Create an account
					</h2>
					<Link
						href={"/sign-in"}
						className="font-normal tracking-[-1%] text-[#323539]"
					>
						Already have an account?
						<span className="text-[#195388]"> Sign in</span>
					</Link>
				</div>
				<div className="flex flex-col space-y-6 px-4 md:px-8">
					<div className="space-y-1.5">
						<Label htmlFor="fullName" className="text-[#323539]">
							Full Name <span className="text-[#c9312c]">*</span>
						</Label>
						<Input
							id="fullName"
							{...register("name")}
							aria-describedby={
								errors.name ? "nameError" : undefined
							}
						/>
						{errors.name?.message && (
							<small
								id="nameError"
								role="alert"
								className="mt-1.5 text-sm text-[#c9312c]"
							>
								{errors.name?.message}
							</small>
						)}
					</div>

					<div className="space-y-1.5">
						<Label htmlFor="email" className="text-[#323539]">
							Email Address{" "}
							<span className="text-[#c9312c]">*</span>
						</Label>
						<Input
							id="email"
							max={254}
							{...register("email")}
							aria-describedby={
								errors.email ? "emailError" : undefined
							}
						/>
						{errors.email?.message && (
							<small
								id="emailError"
								role="alert"
								className="mt-1.5 text-sm text-[#c9312c]"
							>
								{errors.email?.message}
							</small>
						)}
					</div>

					<div className="space-y-1.5">
						<Label htmlFor="password" className="text-[#323539]">
							Password <span className="text-[#c9312c]">*</span>
						</Label>
						<InputIcon
							id="password"
							type={passwordType}
							{...register("password")}
							aria-describedby={
								errors.password
									? "passwordHelp passwordError"
									: "passwordHelp"
							}
							outerClassName="flex flex-row-reverse"
							bigIcon={
								passwordType === "password" ? (
									<LuEyeOff
										onClick={() =>
											setPasswordType(
												passwordType === "password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								) : (
									<LuEye
										onClick={() =>
											setPasswordType(
												passwordType === "password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								)
							}
						/>
						{!errors.password?.message && (
							<small
								id="passwordHelp"
								className={cn("mt-1.5 text-sm text-[#323539]", {
									"text-[#c9312c]": errors.password?.message,
								})}
							>
								Password must contain at least one uppercase
								letter, one lowercase letter, one number, and
								one special character
							</small>
						)}
						{errors.password?.message && (
							<small
								id="passwordError"
								role="alert"
								className="text-sm text-[#c9312c]"
							>
								{errors.password?.message}
							</small>
						)}
					</div>

					<div className="space-y-1.5">
						<Label
							htmlFor="confirmPassword"
							className="text-[#323539]"
						>
							Confirm Password{" "}
							<span className="text-[#c9312c]">*</span>
						</Label>
						<InputIcon
							id="password_confirmation"
							type={passwordConfirmationType}
							{...register("password_confirmation")}
							aria-describedby={
								errors.password_confirmation
									? "confirmPasswordError"
									: undefined
							}
							outerClassName="flex flex-row-reverse"
							bigIcon={
								passwordConfirmationType === "password" ? (
									<LuEyeOff
										onClick={() =>
											setPasswordConfirmationType(
												passwordConfirmationType ===
													"password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								) : (
									<LuEye
										onClick={() =>
											setPasswordConfirmationType(
												passwordConfirmationType ===
													"password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								)
							}
						/>
						{errors.password_confirmation?.message && (
							<small
								id="confirmPasswordError"
								role="alert"
								className="text-sm text-[#c9312c]"
							>
								{errors.password_confirmation?.message}
							</small>
						)}
					</div>
				</div>
				{errors.root?.message && (
					<small
						id="formError"
						role="alert"
						className="mt-1.5 px-4 text-sm text-[#c9312c] md:px-8"
					>
						{errors.root?.message}
					</small>
				)}

				<div className="px-4 md:px-8">
					<div className="flex items-start space-x-2">
						<Checkbox
							handleCheckboxChange={() => {
								setValue(
									"agree_to_terms",
									!getValues("agree_to_terms")
								);
							}}
							id={"agree-to-terms"}
							isChecked={watch("agree_to_terms")}
							className="size-4"
							containerClassName="mt-1"
						/>
						<div className="">
							By creating an account, you agree to
							Migranium&apos;s{" "}
							<Link
								href="/terms"
								className="text-base tracking-[-1%] text-[#195388] underline"
								aria-label="Migranium terms of service"
							>
								Terms
							</Link>{" "}
							and{" "}
							<Link
								href="/privacy-policy"
								className="text-base tracking-[-1%] text-[#195388] underline"
								aria-label="Migranium privacy policy"
							>
								Policies
							</Link>{" "}
						</div>
					</div>
					{errors.agree_to_terms?.message && (
						<small
							id="agreeToTermsError"
							role="alert"
							className="mt-1.5 text-sm text-[#c9312c]"
						>
							{errors.agree_to_terms?.message}
						</small>
					)}
				</div>

				<div className="flex flex-col items-stretch space-y-4 rounded-b-[10px] bg-[#FAFBFC] px-8 pb-4 pt-[18px]">
					<LoaderButton
						disabled={registerUserMutaion.isPending}
						loading={registerUserMutaion.isPending}
						className="h-10 w-full text-white"
						type="submit"
						loaderSize={20}
						aria-label="Create your Migranium account"
					>
						Sign up
					</LoaderButton>

					<p className="text-center text-sm text-[#858C95]">
						Or Sign up With
					</p>
					<div className="flex sm:space-x-2 msm:flex-col msm:space-y-2">
						<SigninWIthGoogle type="sign-up" />

						<SignInWithMicrosoft type="sign-up" />
					</div>
				</div>
				<RequestIsLoading
					isWhite
					isLoading={googleOauthRegisterMutation.isPending}
				/>
			</form>
			{/* <VerifyEmail
				email={watch("email")}
				showVerifyEmailModal={showVerifyEmailModal}
				setShowVerifyEmailModal={setShowVerifyEmailModal}
			/> */}
		</>
	);
};

export default SignUpCard;
