import { z } from "zod";

export const ForgotParams = z.object({
	email: z
		.string()
		.min(4, { message: "Email must be at least 4 characters" })
		.email({ message: "Invalid email address format" }),
});

export const ResetParams = z
	.object({
		token: z.string(),
		password: z
			.string()
			.min(8, { message: "Password must be at least 8 characters long" })
			.regex(/[A-Z]/, {
				message: "Password must contain at least one uppercase letter",
			})
			.regex(/[a-z]/, {
				message: "Password must contain at least one lowercase letter",
			})
			.regex(/\d/, {
				message: "Password must contain at least one number",
			})
			.regex(/[@$!%*?&#]/, {
				message: "Password must contain at least one special character",
			}),
		password_confirmation: z.string(),
	})
	.refine(
		(values) => {
			return values.password === values.password_confirmation;
		},
		{
			message: "Passwords must match!",
			path: ["password_confirmation"],
		}
	);

export interface ResetPasswordDataResponse {
	product_types: string[];
	message: string;
	success: boolean;
}

export interface ResetPasswordDataErrorResponse {
	success: boolean;
	message: Record<string, string[]>;
}

export type ForgotSchema = z.infer<typeof ForgotParams>;
export type ResetSchema = z.infer<typeof ResetParams>;

export interface ForgotPasswordResponse {
	success: boolean;
	message: string;
	data: object;
}

export interface ForgotPasswordErrorResponse {
	status: boolean;
	message: string;
	error: object;
}
