"use client";

import { FormEvent, useState } from "react";
import { TenantConfig } from "@/types/tenant";
import { useSearchSSOEmail, useVerifySSO } from "@/store/slices/auth";

interface SSOSignInCardProps {
	onBack: () => void;
	tenantConfig: TenantConfig | null;
}

interface SSOResponse {
	data: {
		id: number;
		name: string;
		role: string;
		products: string[];
		logo_url: string | null;
		domain: string;
		domain_changed: number;
		sso_enable: boolean;
		sso_login_url: string;
	};
	status: boolean;
	message: string;
}

const formatDomain = (domain: string): string => {
	return domain ? `${domain}.migranium.com` : "";
};

const SSOLoginForm: React.FC<SSOSignInCardProps> = ({
	onBack,
	tenantConfig,
}) => {
	const [companyDomain, setCompanyDomain] = useState(
		tenantConfig?.subdomain || ""
	);
	const [companyEmail, setCompanyEmail] = useState("");
	const [inputType, setInputType] = useState<"domain" | "email">("domain");
	const [error, setError] = useState<string | null>(null);
	const [isLoading, setIsLoading] = useState(false);

	const { isLoading: domainLoading, refetch: refetchDomain } = useVerifySSO(
		inputType === "domain" ? formatDomain(companyDomain) : ""
	);

	const { isLoading: emailLoading, refetch: refetchEmail } =
		useSearchSSOEmail(inputType === "email" ? companyEmail : "");

	const processSSO = (response: SSOResponse) => {
		if (response?.status && response?.data) {
			if (response.data.sso_enable) {
				if (response.data.sso_login_url) {
					window.location.href = response.data.sso_login_url;
				} else {
					setError(
						"SSO is enabled for this domain, but no login URL is configured. Please contact support."
					);
				}
			} else {
				console.log("error-sso", response);
				setError(
					"SSO is not enabled for this domain. Please try standard login."
				);
			}
		} else {
			console.log("error-sso", response);
			setError(
				response?.message ||
					"Failed to verify SSO domain. Please try again."
			);
		}
	};

	const handleSubmit = async (e: FormEvent) => {
		e.preventDefault();
		setError(null);
		setIsLoading(true);

		try {
			if (inputType === "domain") {
				const result = await refetchDomain();
				if (result.data) {
					processSSO(result.data as unknown as SSOResponse);
				}
			} else if (inputType === "email") {
				const result = await refetchEmail();
				if (result.data) {
					console.log(result.data);
					const emailResponse = result.data as unknown as SSOResponse;
					if (emailResponse.status && emailResponse.data?.domain) {
						// setCompanyDomain(emailResponse.data.domain);
						// const domainResult = await refetchDomain();
						processSSO(emailResponse);
						// if (domainResult.data) {
						// 	processSSO(
						// 		domainResult.data as unknown as SSOResponse
						// 	);
						// }
					} else {
						setError("No SSO domain found for this email address.");
					}
				} else {
					setError("No SSO domain found for this email address.");
				}
			}
		} catch (err) {
			setError("An error occurred. Please try again later.");
		} finally {
			setIsLoading(false);
		}
	};

	return (
		<div className="z-1 flex w-[440px] flex-col items-center rounded-xl bg-white">
			<div className="w-full px-6">
				<div className="w-full py-6">
					<h2 className="text-[22px] font-semibold text-[#323539]">
						Sign in with SSO
					</h2>
				</div>

				<form onSubmit={handleSubmit} className="w-full py-4">
					{inputType === "domain" ? (
						<div>
							<label className="mb-1 block text-sm font-medium text-[#323539]">
								Company&apos;s Migranium URL
							</label>
							<div className="flex rounded-lg border border-[#E5E7EB]">
								<input
									type="text"
									value={companyDomain}
									onChange={(e) =>
										setCompanyDomain(e.target.value)
									}
									className="flex-1 rounded-l-lg border-none px-3 py-2 text-sm focus:outline-none"
									placeholder="your-company"
								/>
								<span className="flex items-center bg-[#F3F4F6] px-3 text-sm text-[#6B7280]">
									.migranium.com
								</span>
							</div>
							<p className="py-4 text-center text-sm text-[#6B7280]">
								Don&apos;t know your company URL?{" "}
								<a
									href="#"
									className="text-primary underline underline-offset-2"
									onClick={(e) => {
										e.preventDefault();
										setInputType("email");
										setError(null);
									}}
								>
									Find your company
								</a>
							</p>
						</div>
					) : (
						<div>
							<label className="mb-1 block text-sm font-medium text-[#323539]">
								Company Email Address
							</label>
							<input
								type="email"
								value={companyEmail}
								onChange={(e) =>
									setCompanyEmail(e.target.value)
								}
								className="w-full rounded-lg border border-[#E5E7EB] px-3 py-2 text-sm focus:outline-none"
								placeholder="<EMAIL>"
							/>
							<p className="py-4 text-center text-sm text-[#6B7280]">
								I know my company URL.{" "}
								<a
									href="#"
									className="text-primary underline underline-offset-4"
									onClick={(e) => {
										e.preventDefault();
										setInputType("domain");
										setError(null);
									}}
								>
									Use company url
								</a>
							</p>
						</div>
					)}

					{error && (
						<p className="mt-2 text-sm text-red-500">{error}</p>
					)}
				</form>
			</div>
			<div className="flex w-full justify-between bg-[#FAFBFC] px-6 py-4">
				<button
					type="button"
					onClick={onBack}
					className="rounded-lg bg-white px-4 py-2 text-sm font-medium text-primary"
				>
					Back
				</button>
				<button
					type="submit"
					onClick={handleSubmit}
					disabled={isLoading || domainLoading || emailLoading}
					className="rounded-lg bg-primary px-6 py-2 text-sm font-medium text-white disabled:bg-opacity-70"
				>
					{isLoading || domainLoading || emailLoading
						? "Loading..."
						: "Next"}
				</button>
			</div>
		</div>
	);
};

export default SSOLoginForm;
