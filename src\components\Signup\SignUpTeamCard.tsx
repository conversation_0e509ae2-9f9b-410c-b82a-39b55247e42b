"use client ";

import RequestIsLoading from "@/components/RequestIsLoading";
import { cn } from "@/lib/utils";
import {
	AcceptTeamMemberInvitationSlice,
	VerifyTeamMemberInvitationSlice,
} from "@/store/slices/team-member";
import {
	AcceptTeamMemberSchema,
	AcceptTeamMemberType,
} from "@/types/team-member";
import { handleApiErrors } from "@/utils/general";
import { zodResolver } from "@hookform/resolvers/zod";
import Link from "next/link";
import { useRouter, useSearchParams } from "next/navigation";
import React from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import useUserStore from "../../store/useUserStore";
import useCustomToast from "../CustomToast";
import { LoaderButton } from "../ui-extended/loader-button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import InputIcon from "../ui-extended/input-icon";
import { LuEyeOff, LuEye } from "react-icons/lu";
import Checkbox from "../ui-extended/checkbox";

const SignUpCard: React.FC = () => {
	const [passwordType, setPasswordType] = React.useState("password");
	const [passwordConfirmationType, setPasswordConfirmationType] =
		React.useState("password");

	const router = useRouter();

	const searchParams = useSearchParams();
	const token = searchParams.get("token");
	const role = searchParams.get("role");

	const verifyTeamMemberInvitationQuery =
		VerifyTeamMemberInvitationSlice(token);

	const acceptTeamMemberInvitationMutation =
		AcceptTeamMemberInvitationSlice();

	const {
		register,
		handleSubmit,
		setError,
		formState: { errors },
		setValue,
		getValues,
		watch,
		reset,
	} = useForm<AcceptTeamMemberType>({
		resolver: zodResolver(AcceptTeamMemberSchema),
	});
	const customToast = useCustomToast();
	const handleLoginSuccess = useHandleLoginSuccess();

	const onSubmit: SubmitHandler<AcceptTeamMemberType> = async (data) => {
		acceptTeamMemberInvitationMutation.mutate(data, {
			onSuccess: (data) => {
				customToast("Account created successfully 🎉", {
					id: "reset-password",
				});
				handleLoginSuccess(data);
				// setTimeout(() => {
				// 	router.push("/2fa/verify");
				// }, 1000);
			},
			onError: (error) => {
				if (error.response?.data) {
					if ("error" in error.response.data)
						customToast("Token has expired!", {
							id: "reset-password",
							type: "error",
						});
					if ("errors" in error.response.data)
						handleApiErrors(error.response.data.errors, setError);
				}
			},
		});
	};

	React.useEffect(() => {
		if (!token || !role) return router.replace("/sign-in");
		reset({
			token,
		});
	}, []);

	// console.log(errors);

	return (
		<>
			<form
				className="relative z-10 flex w-full max-w-[488px] flex-col space-y-4 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
				onSubmit={handleSubmit(onSubmit)}
			>
				<div className="flex flex-col space-y-2 px-4 pb-6 md:px-8">
					<h2 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
						Create your organisation account
					</h2>
				</div>
				<div className="flex flex-col space-y-6 px-4 md:px-8">
					<div className="space-y-1.5">
						<Label htmlFor="password" className="text-[#323539]">
							Password <span className="text-[#c9312c]">*</span>
						</Label>
						<InputIcon
							id="password"
							{...register("password")}
							type={passwordType}
							outerClassName="flex flex-row-reverse"
							bigIcon={
								passwordType === "password" ? (
									<LuEyeOff
										onClick={() =>
											setPasswordType(
												passwordType === "password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								) : (
									<LuEye
										onClick={() =>
											setPasswordType(
												passwordType === "password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								)
							}
							aria-describedby={
								errors.password
									? "passwordHelp passwordError"
									: "passwordHelp"
							}
						/>
						{!errors.password?.message && (
							<small
								id="passwordHelp"
								className={cn("mt-1.5 text-sm text-[#323539]", {
									"text-[#c9312c]": errors.password?.message,
								})}
							>
								Password must contain at least one uppercase
								letter, one lowercase letter, one number, and
								one special character
							</small>
						)}
						{errors.password?.message && (
							<small
								id="passwordError"
								role="alert"
								className="text-sm text-[#c9312c]"
							>
								{errors.password?.message}
							</small>
						)}
					</div>

					<div className="space-y-1.5">
						<Label
							htmlFor="confirmPassword"
							className="text-[#323539]"
						>
							Confirm Password{" "}
							<span className="text-[#c9312c]">*</span>
						</Label>
						<InputIcon
							id="password_confirmation"
							type={passwordConfirmationType}
							outerClassName="flex flex-row-reverse"
							bigIcon={
								passwordConfirmationType === "password" ? (
									<LuEyeOff
										onClick={() =>
											setPasswordConfirmationType(
												passwordConfirmationType ===
													"password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								) : (
									<LuEye
										onClick={() =>
											setPasswordConfirmationType(
												passwordConfirmationType ===
													"password"
													? "text"
													: "password"
											)
										}
										className="cursor-pointer text-main-1"
									/>
								)
							}
							{...register("password_confirmation")}
							aria-describedby={
								errors.password_confirmation
									? "confirmPasswordError"
									: undefined
							}
						/>
						{errors.password_confirmation?.message && (
							<small
								id="confirmPasswordError"
								role="alert"
								className="text-sm text-[#c9312c]"
							>
								{errors.password_confirmation?.message}
							</small>
						)}
					</div>
				</div>
				{errors.root?.message && (
					<p
						id="formError"
						role="alert"
						className="mt-1.5 px-4 text-sm text-[#c9312c] md:px-8"
					>
						{errors.root?.message}
					</p>
				)}

				<div className="px-4 md:px-8">
					<div className="flex items-start space-x-2">
						<Checkbox
							handleCheckboxChange={() => {
								setValue(
									"agree_to_terms",
									!getValues("agree_to_terms")
								);
							}}
							id={"agree-to-terms"}
							isChecked={watch("agree_to_terms")}
							className="size-4"
							containerClassName="mt-1"
						/>
						<div className="">
							By creating an account, you agree to
							Migranium&apos;s{" "}
							<Link
								href="/terms"
								className="text-base tracking-[-1%] text-[#195388] underline"
								aria-label="Migranium terms of service"
							>
								Terms
							</Link>{" "}
							and{" "}
							<Link
								href="/privacy-policy"
								className="text-base tracking-[-1%] text-[#195388] underline"
								aria-label="Migranium privacy policy"
							>
								Policies
							</Link>{" "}
						</div>
					</div>
					{errors.agree_to_terms?.message && (
						<small
							id="agreeToTermsError"
							role="alert"
							className="mt-1.5 text-sm text-[#c9312c]"
						>
							{errors.agree_to_terms?.message}
						</small>
					)}
				</div>

				<div className="flex flex-col items-stretch space-y-4 rounded-b-[10px] bg-[#FAFBFC] px-8 pb-4 pt-[18px]">
					<LoaderButton
						disabled={acceptTeamMemberInvitationMutation.isPending}
						loading={acceptTeamMemberInvitationMutation.isPending}
						className="h-10 w-full text-white"
						type="submit"
						loaderSize={20}
						aria-label="Join your organisation"
					>
						Sign up
					</LoaderButton>
				</div>
				<RequestIsLoading
					isWhite
					isLoading={verifyTeamMemberInvitationQuery.isLoading}
					size={20}
				/>
			</form>
		</>
	);
};

export default SignUpCard;
