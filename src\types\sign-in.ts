export interface AuthLoginResponse {
	status: boolean;
	message: string;
	data: AuthUserData;
}

export interface AuthErrorInvalidResponse {
	status: boolean;
	message: string;
	error: string;
}

export interface AuthUserData {
	id: number;
	name: string;
	email: string;
	is_email_verified: boolean;
	is_active: boolean;
	businesses?: AuthBusiness[];
	default_business_id?: number;
	company_id: string;
	token: string;
	expires_in: number;
	two_factor_enable: boolean;
	two_factor_skip: boolean;
	remember_token?: string;
	twoFactor?: boolean;
}

export interface AuthBusiness {
	id: number;
	name: string;
	role: string;
	products: ("primary" | "spaces")[];
	domain: string;
	domain_changed: number;
	sso_enable: boolean;
	sso_login_url: string;
}

export interface AuthApplications {
	spaces: boolean;
	workflow: boolean;
}

export interface AuthSendOTPToEmailResponse {
	status: boolean;
	message: string;
}
