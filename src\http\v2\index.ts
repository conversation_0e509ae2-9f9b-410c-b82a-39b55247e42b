import { AddBusinessLocationResponseType } from "@/types/location";
import {
	GetMFASettingsResponse,
	ManualVerifyApp2FAProps,
	SendCodeToEmailProps,
	VerifyApp2FAProps,
} from "@/types/mfa";
import {
	AddBusinessOperatingHoursData,
	OnboardingFormType,
	OnboardingImageUploadSuccessResponse,
	OnboardingSuccessResponse,
} from "@/types/onboarding";
import { AuthLoginResponse } from "@/types/sign-in";
import {
	AcceptTeamMemberResponse,
	AcceptTeamMemberType,
} from "@/types/team-member";
import $http from "../xhr";
import { headers } from "next/headers";

export const APIVersion2GoogleOauthLogin = async (data: {
	token: string;
	type: "sign-in" | "sign-up";
}): Promise<AuthLoginResponse> =>
	$http.post("/oauth/google/callback", data).then((res) => res.data);

export const APIVersion2MicrosoftOauthRegister = async (data: {
	token: string;
}): Promise<AuthLoginResponse> =>
	$http.post("/sign-up-with-ms", data).then((res) => res.data);

export const APIVersion2MicrosoftOauthLogin = async (data: {
	token: string;
}): Promise<AuthLoginResponse> =>
	$http.post("/sign-in-with-ms", data).then((res) => res.data);

export const APIVersion2AddLocation = async (
	data: FormData
): Promise<AddBusinessLocationResponseType> =>
	$http.post("/v2/my-business/locations/add", data);

export const APIVersion2UpdateBusinessOperatingHours = async (
	data: AddBusinessOperatingHoursData
): Promise<unknown> => $http.post("/v2/my-business/update-all", data);

//MFA
export const APIVersion2Get2faSettings =
	async (): Promise<GetMFASettingsResponse> =>
		$http.get("/2fa/enable").then((res) => res.data);

export const APIVersion2Verify2faAppOTP = async (
	data: VerifyApp2FAProps
): Promise<AuthLoginResponse> => {
	return $http.post("/2fa/verify", data).then((res) => res.data);
};

export const APIVersion2SendCodeToEmail = async (
	data: SendCodeToEmailProps
): Promise<AuthLoginResponse> => {
	return $http.post("/2fa/code/email", data).then((res) => res.data);
};

export const APIVersion2ManualVerify2faAppOTP = async (
	data: ManualVerifyApp2FAProps
): Promise<any> => {
	return $http.post("/2fa/confirm", data).then((res) => res.data);
};

export const APIVersion2Skip2fa = async (): Promise<any> => {
	return $http.post("/2fa/skip").then((res) => res.data);
};

export const APIVersion2LoginTemp = async (data: {
	token: string;
}): Promise<any> => {
	return $http.post("/login/temp", data).then((res) => res.data);
};

// Onboarding
export const APIVersion2OnboardingNew = async (
	data: OnboardingFormType
): Promise<OnboardingSuccessResponse> => {
	return $http.post("/v2/onboarding", data).then((res) => res.data);
};

export const APIVersion2OnboardingImageUpload = async (
	data: FormData
): Promise<OnboardingImageUploadSuccessResponse> => {
	return $http
		.post("/v2/image/upload", data, {
			headers: {
				"Content-Type": "multipart/form-data",
			},
		})
		.then((res) => res.data);
};

// Team Member
export const APIVersion2TeamMemberAccept = async (
	data: AcceptTeamMemberType
): Promise<AuthLoginResponse> =>
	$http
		.post("/v2/accept-team-member-invitation", {
			...data,
			password_confirmation: undefined,
			agree_to_terms: undefined,
		})
		.then((res) => res.data);

export const APIVersion2TeamMemberVerifyInvitation = async (data: {
	token: string | null;
}): Promise<unknown> =>
	$http
		.get("/v2/check-team-member-invitation", {
			params: { token: data.token },
		})
		.then((res) => res.data);

export const APIVersion2VerifySSODomain = async (
	domain?: string
): Promise<any> => {
	const params = new URLSearchParams();
	if (domain) {
		params.append("domain", domain);
	}

	return $http
		.get(`/v2/verify${params.toString() ? `?${params.toString()}` : ""}`)
		.then((res) => res.data);
};

export const APIVersion2VerifyTenant = async (
	domain?: string
): Promise<any> => {
	return $http
		.get(`/v2/verify`, {
			headers: {
				"request-domain": domain,
			},
		})
		.then((res) => res.data);
};

export const APIVersion2SearchDomainForSSO = async (
	email: string
): Promise<any> => {
	return $http
		.get(`/v2/search-domain?email=${email}`)
		.then((res) => res.data);
};

export const APIVersion2SSOUserVerify = async (token: string) => {
	return $http
		.get(`/v2/sso-user-verify?token=${token}`)
		.then((res) => res.data);
};
