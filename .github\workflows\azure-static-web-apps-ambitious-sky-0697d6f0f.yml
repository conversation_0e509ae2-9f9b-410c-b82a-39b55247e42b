name: Azure Static Web Apps CI/CD

on:
    push:
        branches:
            - dev
    pull_request:
        types: [opened, synchronize, reopened, closed]
        branches:
            - dev

jobs:
    build_and_deploy_job:
        if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
        runs-on: ubuntu-latest
        env:
            NODE_OPTIONS: --max_old_space_size=4096

        name: Build and Deploy Job
        steps:
            - uses: actions/checkout@v3
              with:
                  submodules: true
                  lfs: false

            - name: Download and Load Environment Variables
              run: |
                  echo "Downloading environment variables..."
                  curl -o .env "${{ secrets.DEV_AZURE_BLOB_SAS_URL }}"

                  echo "Parsing environment variables..."
                  export $(grep -v '^#' .env | xargs)

                  echo "Environment variables loaded!"

            - name: Build And Deploy
              id: builddeploy
              uses: Azure/static-web-apps-deploy@v1
              with:
                  azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_AMBITIOUS_SKY_0697D6F0F }}
                  repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
                  action: "upload"
                  ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
                  # For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
                  app_location: "/" # App source code path
                  api_location: "" # Api source code path - optional
                  output_location: "" # Built app content directory - optional
                  ###### End of Repository/Build Configurations ######

    close_pull_request_job:
        if: github.event_name == 'pull_request' && github.event.action == 'closed'
        runs-on: ubuntu-latest
        name: Close Pull Request Job
        steps:
            - name: Close Pull Request
              id: closepullrequest
              uses: Azure/static-web-apps-deploy@v1
              with:
                  azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_AMBITIOUS_SKY_0697D6F0F }}
                  action: "close"
