import React, { useState, useEffect, useRef, useCallback } from "react";
import first from "../../../public/assets/first.png";
import second from "../../../public/assets/second.png";
import third from "../../../public/assets/third.png";
import fourth from "../../../public/assets/fourth.png";

export default function CustomAccordion() {
	const [activeItem, setActiveItem] = useState("item-1");
	const [isInView, setIsInView] = useState(false);
	const accordionRef = useRef<HTMLDivElement>(null);
	const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
	const isScrolling = useRef(false);

	const accordionData = [
		{
			id: "item-1",
			title: "Highly Adaptable to Your Unique Workflow",
			content:
				"Every practice operates differently. Migranium adapts to yours. Whether you're high-volume or highly specialized, easily customize settings to fit your specific workflow and preferences.",
			image: first.src,
		},
		{
			id: "item-2",
			title: "AI That Saves Time and Delivers Value",
			content:
				"Leverage artificial intelligence to streamline your processes and deliver exceptional value to your clients with automated workflows and intelligent insights.",
			image: second.src,
		},
		{
			id: "item-3",
			title: "Get Started In Days With No Downtime",
			content:
				"Quick implementation process that gets you up and running without disrupting your current operations or causing any downtime.",
			image: third.src,
		},
		{
			id: "item-4",
			title: "Manage Everything In One Connected Platform",
			content:
				"Centralize all your operations in a single, integrated platform that connects all aspects of your workflow for maximum efficiency.",
			image: fourth.src,
		},
	];

	// Get current item index
	const getCurrentItemIndex = () => {
		return accordionData.findIndex((item) => item.id === activeItem);
	};

	// Check if we're at boundaries
	const isAtFirstItem = () => getCurrentItemIndex() === 0;
	const isAtLastItem = () =>
		getCurrentItemIndex() === accordionData.length - 1;

	// Navigate to next/previous item
	const navigateToItem = useCallback(
		(direction: "up" | "down") => {
			const currentIndex = getCurrentItemIndex();
			let newIndex;

			if (direction === "down") {
				newIndex =
					currentIndex < accordionData.length - 1
						? currentIndex + 1
						: currentIndex;
			} else {
				newIndex = currentIndex > 0 ? currentIndex - 1 : currentIndex;
			}

			if (newIndex !== currentIndex) {
				setActiveItem(accordionData[newIndex].id);
				return true; // Item changed
			}
			return false; // No change
		},
		[accordionData, activeItem]
	);

	// Intersection Observer to detect when accordion is in view
	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				setIsInView(entry.isIntersecting);
			},
			{
				threshold: 0.3, // Trigger when 30% of the accordion is visible (NB: You change this value based on the responsiveness you want)
				rootMargin: "-10% 0px -10% 0px", // Add some margin to make it more precise
			}
		);

		if (accordionRef.current) {
			observer.observe(accordionRef.current);
		}

		return () => {
			if (accordionRef.current) {
				observer.unobserve(accordionRef.current);
			}
		};
	}, []);

	// Handle scroll events with custom logic
	useEffect(() => {
		const handleScroll = (e: WheelEvent) => {
			// Only handle scroll when accordion is in view
			if (!isInView) return;

			const scrollDirection = e.deltaY > 0 ? "down" : "up";

			// Check if we should allow normal scrolling at boundaries
			const shouldAllowNormalScroll =
				(scrollDirection === "up" && isAtFirstItem()) ||
				(scrollDirection === "down" && isAtLastItem());

			if (shouldAllowNormalScroll) {
				// Allow normal scroll behavior at boundaries
				return;
			}

			// Prevent default scroll and handle accordion navigation
			e.preventDefault();

			// Clear existing timeout
			if (scrollTimeout.current) {
				clearTimeout(scrollTimeout.current);
			}

			// Debounce scroll events
			scrollTimeout.current = setTimeout(() => {
				if (isScrolling.current) return;

				isScrolling.current = true;
				navigateToItem(scrollDirection);

				// Reset scrolling flag after a delay
				setTimeout(() => {
					isScrolling.current = false;
				}, 600); // Prevent rapid switching
			}, 50); // Shorter debounce for better responsiveness
		};

		// Add wheel event listener (for mouse wheel and trackpad)
		window.addEventListener("wheel", handleScroll, { passive: false });

		// Cleanup
		return () => {
			window.removeEventListener("wheel", handleScroll);
			if (scrollTimeout.current) {
				clearTimeout(scrollTimeout.current);
			}
		};
	}, [isInView, navigateToItem]);

	// Toggle accordion item
	const toggleItem = (itemId: string) => {
		setActiveItem(itemId === activeItem ? "" : itemId);
	};

	return (
		<section className="">
			<div>
				<div className="text-center">
					<span className="mb-2 text-lg font-semibold text-[#01B18B]">
						What Makes Us Different
					</span>
					<h2 className="mb-2 text-xl font-bold text-[#0C0D0E] sm:text-3xl md:text-[32px]">
						Built Specifically for Complex Healthcare Workflows{" "}
					</h2>
					<p className="text-sm text-[#68778D] sm:text-lg">
						Adapts effortlessly to any workflow, is easy to use,
						powered by AI to enhance how you work.
					</p>
				</div>
			</div>

			{/* Accordion section */}
			<div
				ref={accordionRef}
				className="mx-auto mb-[45px] mt-12 flex w-full flex-col-reverse items-center justify-between font-inter md:max-w-[1216px] md:px-10 lg:flex-row"
			>
				<div className="mx-auto flex w-full flex-col-reverse items-center justify-between lg:flex-row">
					{/* Left side - Custom Accordion */}
					<div className="space-y-6">
						<div className="mx-auto w-full px-3 md:px-0">
							{accordionData.map((item, index) => (
								<div key={item.id} className="">
									<button
										aria-expanded={activeItem === item.id}
										aria-controls={`content-${item.id}`}
										onClick={() => toggleItem(item.id)}
										className={`flex w-full items-start border-l-4 pl-2 text-left outline-none transition-all duration-200 sm:pr-4 md:pl-6 ${
											activeItem === item.id
												? "border-teal-500"
												: "border-gray-300"
										}`}
									>
										<div className="flex w-full items-start">
											<div
												className={`flex-1 ${activeItem === item.id ? "text-[#0C0D0E]" : "text-[#0C0D0E]/40"} ${index !== accordionData.length - 1 ? "mb-6" : ""}`}
											>
												<h3
													className={`text-base font-semibold leading-tight transition-colors duration-200 md:text-lg`}
												>
													{item.title}
												</h3>
												{activeItem === item.id &&
													item.content && (
														<div className="mt-3 max-w-[497px] text-sm leading-relaxed text-[#303741] md:text-base">
															{item.content}
														</div>
													)}
											</div>
										</div>
									</button>
								</div>
							))}
						</div>
					</div>

					{/* Right side - Dynamic Interface Mockup */}
					<div className="flex justify-center lg:justify-end">
						<div className="relative">
							{/* Background circle */}
							<div className="flex h-[190px] w-[190px] items-center justify-center overflow-hidden lg:h-[467px] lg:w-[467px]">
								{/* Dynamic images with fade transition */}
								{accordionData.map((item) => (
									<div
										key={item.id}
										className={`absolute inset-0 flex items-center justify-center transition-opacity duration-500 ${
											activeItem === item.id
												? "opacity-100"
												: "opacity-0"
										}`}
									>
										<div className="relative h-full w-full">
											<img
												src={
													item.image ||
													"/placeholder.svg"
												}
												alt={`Interface for ${item.title}`}
												className="rounded-full object-cover"
											/>
										</div>
									</div>
								))}
							</div>
							{/* Decorative curved line */}
							{/* <div className="pointer-events-none absolute -bottom-8 -left-8 h-full w-full">
								<svg
									className="h-full w-full"
									viewBox="0 0 100 100"
									fill="none"
									xmlns="http://www.w3.org/2000/svg"
								>
									<path
										d="M20 80 Q50 60 80 80"
										stroke="#1f2937"
										strokeWidth="2"
										fill="none"
									/>
								</svg>
							</div> */}
						</div>
					</div>
				</div>
			</div>
		</section>
	);
}

// {
// 	accordionData.map((item) => (
// 		<div key={item.id} className="custom-accordion-item">
// 			{/* Accordion Header */}
// 			<button
// 				onClick={() => toggleItem(item.id)}
// 				className={`w-full rounded-sm text-left transition-all duration-200 ${
// 					activeItem === item.id
// 						? "border-l-4 border-l-teal-400 pl-2 text-[#0C0D0E] md:pl-6"
// 						: "pl-0 text-[#0C0D0E]/40"
// 				}`}
// 				aria-expanded={activeItem === item.id}
// 				aria-controls={`content-${item.id}`}
// 			>
// 				<h3 className="text-base font-semibold leading-tight transition-colors duration-200 md:text-lg">
// 					{item.title}
// 				</h3>
// 			</button>

// 			{/* Accordion Content */}
// 			<div
// 				id={`content-${item.id}`}
// 				role="region"
// 				aria-labelledby={`header-${item.id}`}
// 				className={`overflow-hidden transition-all duration-300 ${
// 					activeItem === item.id
// 						? "max-h-96 border-l-4 border-l-teal-400 pl-6 opacity-100"
// 						: "max-h-0 pt-0 opacity-0"
// 				}`}
// 			>
// 				<p className="mt-3 max-w-[497px] text-sm leading-relaxed text-[#303741] md:text-base">
// 					{item.content}
// 				</p>
// 			</div>
// 		</div>
// 	));
// }
