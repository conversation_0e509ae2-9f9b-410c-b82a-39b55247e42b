"use client";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import useCustomToast from "@/components/CustomToast";
import { UseQueryError } from "@/hooks/useQueryError";
import {
	// APIVersion1GetBusinessInformation,
	// APIVersion1GetSubscriptionPlans,
	APIVersion1GetUserInformation,
	APIVersion1GoogleRegister,
	APIVersion1Login,
	APIVersion1Register,
} from "@/http/v1";
import {
	APIVersion2LoginTemp,
	APIVersion2VerifySSODomain,
	APIVersion2SearchDomainForSSO,
	APIVersion2VerifyTenant,
} from "@/http/v2";
import { AuthLoginResponse } from "@/types/sign-in";
// import { AddLocationType } from "@/types/onboarding";
import {
	AuthTwoEnabledFactorResponse,
	RegisterErrorResponse,
	RegisterResponse,
	SignUpType,
	User,
	UserLoginType,
} from "@/types/signup";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError, AxiosResponse } from "axios";
import { UseFormSetError } from "react-hook-form";
// import useUserStore from "../useUserStore";

export const useRegisterUser = () => {
	return useMutation<
		RegisterResponse,
		AxiosError<RegisterErrorResponse>,
		SignUpType
	>({
		mutationFn: APIVersion1Register,
	});
};

export const useGoogleRegisterUser = (
	onSuccess: (
		data: AxiosResponse<{
			status: boolean;
			message: string;
			user: User;
			token: string;
		}>
	) => void = () => {
		return;
	},
	onError: (error: AxiosError) => void = () => {
		return;
	}
) => {
	const customToast = useCustomToast();

	return useMutation<
		AxiosResponse<{
			status: boolean;
			message: string;
			user: User;
			token: string;
		}>,
		AxiosError<Record<string, string>>,
		{ token: string }
	>({
		mutationFn: APIVersion1GoogleRegister,
		onSuccess,
		onError: (error) => {
			if (
				error.response &&
				error.response.status >= 400 &&
				error.response?.data.message
			)
				customToast(error.response?.data.message, {
					id: "google-signup",
					type: "error",
				});

			onError(error);
		},
	});
};

export const useGetUserInformation = (
	isEnabled: boolean = true,
	onError?: (error: AxiosError | null) => void
) => {
	// const handleLoginSuccess = useHandleLoginSuccess();
	const getUserInformation = useQuery<AxiosResponse<User>, AxiosError>({
		enabled: isEnabled,
		queryKey: ["user-info"],
		queryFn: APIVersion1GetUserInformation,
		retry: 3,
	});

	// useEffect(() => {
	// 	if (getUserInformation?.data?.data)
	// 		handleLoginSuccess({
	// 			user: getUserInformation?.data.data,
	// 			token: getCookie("ac-token") ?? "",
	// 		});
	// }, [getUserInformation.isSuccess]);

	UseQueryError({
		isError: getUserInformation.isError,
		onError: () => onError?.(getUserInformation.error),
	});

	return getUserInformation;
};

export const useGetUserInformationMutation = (
	onSuccess?: (data: AxiosResponse | null) => void,
	onError?: (error: AxiosError | null) => void
) => {
	// const handleLoginSuccess = useHandleLoginSuccess();
	return useMutation<AxiosResponse<User>, AxiosError>({
		mutationFn: APIVersion1GetUserInformation,
		onSuccess: (data) => {
			// handleLoginSuccess({
			// 	user: data.user,
			// 	token: getCookie("ac-token") ?? "",
			// });
			onSuccess?.(data);
		},
		onError,
	});
};

export const useLoginUser = (setError: UseFormSetError<UserLoginType>) => {
	const customToast = useCustomToast();

	return useMutation<
		AuthLoginResponse | AuthTwoEnabledFactorResponse,
		AxiosError<any>,
		UserLoginType
	>({
		mutationFn: APIVersion1Login,

		onError: (error: AxiosError<any>) => {
			if (error.response?.data.message) {
				setError("root", {
					message: "Email or password is incorrect",
				});
				return;
			}

			customToast("An error occured kindly try again later", {
				id: "login",
				type: "error",
			});
		},
	});
};

export const useLoginTempUser = () => {
	const customToast = useCustomToast();
	const handleLoginSuccess = useHandleLoginSuccess();

	return useMutation<
		AuthLoginResponse | AuthTwoEnabledFactorResponse,
		AxiosError<any>,
		{ token: string }
	>({
		mutationFn: APIVersion2LoginTemp,
		onSuccess: handleLoginSuccess,
		onError: (error: AxiosError<any>) => {
			console.error(error);
			customToast("An error occured kindly try again later", {
				id: "login",
				type: "error",
			});
		},
	});
};

export const useVerifySSOTenant = (domain: string | null) => {
	return useQuery<AxiosResponse<User>, AxiosError>({
		enabled: !!domain,
		queryKey: ["verify-sso-tenant", domain],
		queryFn: () => {
			if (!domain) {
				throw new Error("Domain is required for SSO verification");
			}
			return APIVersion2VerifyTenant(domain);
		},
		retry: 3,
	});
};

export const useVerifySSO = (domain: string | null) => {
	return useQuery<AxiosResponse<User>, AxiosError>({
		enabled: false,
		queryKey: ["verify-sso", domain],
		queryFn: () => {
			if (!domain) {
				throw new Error("Domain is required for SSO verification");
			}
			return APIVersion2VerifySSODomain(domain);
		},
		retry: 3,
	});
};

export const useSearchSSOEmail = (email: string) => {
	return useQuery<AxiosResponse, AxiosError>({
		enabled: false,
		queryKey: ["search-sso-domain", email],
		queryFn: () => APIVersion2SearchDomainForSSO(email),
		retry: 1,
	});
};
