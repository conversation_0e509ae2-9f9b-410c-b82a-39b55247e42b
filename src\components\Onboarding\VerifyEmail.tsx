import { Dialog, DialogContent, DialogTitle } from "@/components/ui/dialog";
import { LuX } from "react-icons/lu";
import { Button } from "../ui/button";
import { LoaderButton } from "../ui-extended/loader-button";
import { ResendVerificationEmailSlice } from "@/store/slices/onboarding";
import useCustomToast from "../CustomToast";

const VerifyEmail: React.FC<{
	email: string;
	showVerifyEmailModal: boolean;
	setShowVerifyEmailModal: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ email, showVerifyEmailModal, setShowVerifyEmailModal }) => {
	const customToast = useCustomToast();
	const resendVerificationEmailMutation = ResendVerificationEmailSlice();

	const onSubmitForm = () => {
		resendVerificationEmailMutation.mutate(
			{
				email,
			},
			{
				onSuccess: () => {
					customToast("Verification email sent successfully 🎉", {
						id: "resend-verification-email",
					});
				},

				onError: () => {
					customToast("Verification email could not be sent 🤕", {
						id: "resend-verification-email",
						type: "error",
					});
				},
			}
		);
	};

	return (
		<Dialog
			open={showVerifyEmailModal}
			onOpenChange={setShowVerifyEmailModal}
		>
			<DialogContent className="max-w-[360px] p-4">
				<div className="flex justify-between space-x-2">
					<i className="mgc_invite_line py-1 text-[20px] before:!text-primary" />
					<form className="flex flex-col" onSubmit={onSubmitForm}>
						<DialogTitle className="text-[22px] font-semibold capitalize leading-[30px] -tracking-[1%] text-[#323539]">
							Verify Email
						</DialogTitle>
						<p className="trakcing-[-0.1px] text-[14px] leading-[20px] text-[#6D748D]">
							Check your email for a verification link to complete
							your account setup.
						</p>
						<LoaderButton
							className="mt-4 self-end text-white"
							type="button"
							loaderSize={20}
							loading={resendVerificationEmailMutation.isPending}
							disabled={resendVerificationEmailMutation.isPending}
							onClick={onSubmitForm}
						>
							Resend Email
						</LoaderButton>
					</form>
					<button
						className="flex items-start"
						onClick={(e) => {
							e.preventDefault();
							setShowVerifyEmailModal(false);
						}}
					>
						<LuX
							color="#858C95"
							className="cursor-pointer"
							width="20px"
							height="20px"
						/>
					</button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default VerifyEmail;
