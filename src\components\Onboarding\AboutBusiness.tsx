"use client";

import { SearchableSelect } from "@/components/ui-extended/searchable-select";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectLabel,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	findCountryByLabel,
	findProvinceByLabel,
	useAddressAutocomplete,
} from "@/hooks/useAddressAutoComplete";
import { cn } from "@/lib/utils";
import {
	NewOnboardingImageUploadSlice,
	useGetBusinessCategories,
} from "@/store/slices/onboarding";
import useUserStore from "@/store/useUserStore";
import { businessDetailsSchema, OnboardingFormType } from "@/types/onboarding";
import { countryCodes, countryOptions } from "@/utils/constants";
import { changeCountry, updateCountryAndState } from "@/utils/general";
import React, { useRef, useState } from "react";
import { useFormContext } from "react-hook-form";

const AboutBusiness: React.FC<{
	countryCode: string;
	setCountryCode: React.Dispatch<React.SetStateAction<string>>;
}> = ({ countryCode, setCountryCode }) => {
	const NewOnboardingImageUploadMutation = NewOnboardingImageUploadSlice();
	const { onboardingState, setOnboardingState } = useUserStore((s) => ({
		onboardingState: s.onboardingState,
		setOnboardingState: s.setOnboardingState,
	}));

	const formMethods = useFormContext<OnboardingFormType>();

	const [logoFile, setLogoFile] = useState<null | File>(null);

	const businessCategoriesQuery = useGetBusinessCategories();

	const [provinceOptions, setProvinceOptions] = useState<
		{
			label: string;
			value: string;
		}[]
	>([]);

	const logoRef: any | null = useRef(null);

	const submitLogo = (file: File) => {
		const formData = new FormData();
		if (file) formData.append("file", file);
		NewOnboardingImageUploadMutation.mutate(formData, {
			onSuccess: (data) => {
				formMethods.setValue(
					"business_details.logo",
					data.data.image_url
				);
				formMethods.clearErrors("business_details.logo");
			},
			onError: () => {
				formMethods.setError("business_details.logo", {
					type: "manual",
					message: "Failed to upload logo",
				});
				setLogoFile(null);
				formMethods.setValue("business_details.logo", undefined);
			},
		});
	};

	const addressAutocomplete = useAddressAutocomplete(
		(address, city, province, country, postalCode) => {
			formMethods.setValue("business_details.address", address);
			formMethods.setValue("business_details.city", city);
			formMethods.setValue("business_details.state", province);
			formMethods.setValue("business_details.country", country);
			formMethods.setValue("business_details.zip_code", postalCode);

			const countryValue = findCountryByLabel(country);

			const newProvinceOptions = changeCountry(countryValue);
			setProvinceOptions(newProvinceOptions);

			const newProvince = findProvinceByLabel(
				newProvinceOptions,
				province
			);
			updateCountryAndState(
				formMethods.setValue,
				setProvinceOptions,
				true,
				newProvince,
				countryValue
			);
		}
	);

	const handleFileChange = async (
		event: React.ChangeEvent<HTMLInputElement>
	): Promise<void> => {
		if (event?.target?.files?.[0]) {
			const file = event.target.files[0];
			const validTypes = [
				"image/png",
				"image/jpeg",
				"image/jpg",
				"image/svg+xml",
			];
			const maxSize = 2 * 1024 * 1024; // 2MB in bytes

			if (!validTypes.includes(file.type)) {
				formMethods.setError("business_details.logo", {
					type: "manual",
					message: "Only PNG, JPG, JPEG, and SVG files are allowed",
				});
				setLogoFile(null);
				formMethods.setValue("business_details.logo", undefined);
				return;
			}

			if (file.size > maxSize) {
				formMethods.setError("business_details.logo", {
					type: "manual",
					message: "File size must not exceed 2MB",
				});
				setLogoFile(null);
				formMethods.setValue("business_details.logo", undefined);
				return;
			}

			setLogoFile(file);
			submitLogo(file);
		}
	};

	const handleSubmit = async () => {
		const result = businessDetailsSchema.safeParse(
			formMethods.getValues("business_details")
		);

		if (!result.success) {
			result.error.issues.forEach((issue) => {
				formMethods.setError(
					("business_details." + issue.path[0]) as any,
					{
						type: "manual",
						message: issue.message,
					}
				);
			});
			return false;
		}
		setOnboardingState(2);
		return true;
	};

	const updateBusinessCheckThenUpdateFields = () => {
		updateCountryAndState(
			formMethods.setValue,
			setProvinceOptions,
			true,
			"ON",
			"CA"
		);
	};

	React.useEffect(() => updateBusinessCheckThenUpdateFields(), []);

	return (
		<div
			className={cn(
				"relative flex h-fit max-h-fit w-full max-w-[656px] flex-col space-y-4 rounded-[10px] bg-white shadow-[0px_10px_15px_-3px_rgba(16,24,40,0,1)]",
				{
					hidden: onboardingState === 2,
					flex: onboardingState === 1,
				}
			)}
		>
			<p className="px-8 py-3 text-[22px] font-semibold text-[#323539]">
				Tell us about your organisation
			</p>
			<div className="flex flex-col space-y-7 px-8">
				<div className="space-y-1.5">
					<Label className="text-[#323539]">
						Organisation Name{" "}
						<span className="text-[#c9312c]">*</span>
					</Label>
					<Input {...formMethods.register("business_details.name")} />
					{formMethods.formState.errors.business_details?.name
						?.message && (
						<small className="mt-1.5 text-sm text-[#c9312c]">
							{
								formMethods.formState.errors.business_details
									.name?.message as string
							}
						</small>
					)}
				</div>
				<div className="flex justify-between space-x-4">
					<div className="flex-1">
						<Label className="bt-1.5 mb-1.5 block text-sm font-medium tracking-[-0.1px] text-[#323539]">
							Organisation Category{" "}
							<span className="text-[#c9312c]">*</span>
						</Label>
						<div>
							<Select
								value={formMethods
									.watch(
										"business_details.business_category_id"
									)
									?.toString()}
								onValueChange={(value) => {
									formMethods.setValue(
										"business_details.business_category_id",
										parseInt(value)
									);
								}}
							>
								<SelectTrigger>
									<SelectValue placeholder="Select" />
								</SelectTrigger>
								<SelectContent>
									{businessCategoriesQuery?.data?.data.map(
										(item) => (
											<SelectItem
												key={item.id}
												value={item.id.toString()}
											>
												{item.name}
											</SelectItem>
										)
									)}
								</SelectContent>
							</Select>
							{formMethods.formState.errors.business_details
								?.business_category_id?.message && (
								<small className="mt-1.5 text-sm text-[#c9312c]">
									{
										formMethods.formState.errors
											.business_details
											.business_category_id
											?.message as string
									}
								</small>
							)}
						</div>
					</div>

					{/* <div className="flex-1">
						<Label className="bt-1.5 mb-1.5 block text-sm font-medium tracking-[-0.1px] text-[#323539]">
							Product <span className="text-[#c9312c]">*</span>
						</Label>

						<MultiSelect
							options={ProductTypeOptions.map((option) => ({
								label: option.label,
								value: option.value,
							}))}
							placeholder="Select Products"
							selected={
								((
									formMethods.watch(
										"business_details.products"
									) || []
								).map((item) => ({
									label: ProductTypeOptions.find(
										(option) => option.value === item
									)?.label,
									value: item,
								})) as Option[]) || []
							}
							onSelect={(selected) =>
								formMethods.setValue(
									"business_details.products",
									selected.map((item) => item.value)
								)
							}
							onUnselect={(option) => {
								const newSelected = (
									(formMethods
										.watch("business_details.products")
										.map((item) => ({
											label: ProductTypeOptions.find(
												(option) =>
													option.value === item
											)?.label,
											value: item,
										})) as Option[]) || []
								).filter(
									(item: Option) =>
										item.value !== option.value
								);
								formMethods.setValue(
									"business_details.products",
									newSelected.map((item) => item.value)
								);
							}}
							checkBoxed={true}
							error={
								formMethods.formState.errors.business_details
									?.products as any
							}
						/>
					</div> */}
				</div>
				<div className="flex w-full flex-col space-y-1.5 self-center">
					<Label className="font-medium tracking-[-0.1px] text-[#323539]">
						Phone Number
					</Label>
					<div className="flex max-w-[293px] items-stretch">
						<Select
							value={countryCode}
							onValueChange={(value) => setCountryCode(value)}
						>
							<SelectTrigger className="h-10 w-fit rounded-r-none border-r-transparent">
								<SelectValue placeholder="+1" />
							</SelectTrigger>
							<SelectContent>
								{countryCodes.map((option) => (
									<SelectItem
										key={option.value}
										value={option.value}
										className="px-8"
									>
										{option.label}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						<Input
							className="h-10 self-stretch rounded-l-none border border-[#E4E4E7] py-0"
							{...formMethods.register(
								"business_details.phone_number",
								{
									minLength: 10,
									maxLength: 10,
								}
							)}
						/>
					</div>
					{formMethods.formState.errors.business_details
						?.phone_number &&
						(
							formMethods.formState.errors.business_details
								?.phone_number?.message as string
						)?.length && (
							<small className="text-xs leading-[16px] text-[#c9312c]">
								{
									formMethods.formState.errors
										.business_details.phone_number
										.message as string
								}
							</small>
						)}
				</div>
				<div className="flex w-full flex-1 space-x-7">
					<div className="flex flex-1 flex-col space-y-1.5">
						<Label className="text-[#323539]">
							Address Line{" "}
							<span className="text-[#c9312c]">*</span>
						</Label>

						<SearchableSelect
							options={addressAutocomplete.suggestions.map(
								(suggestion) => ({
									value: suggestion.description,
									label: suggestion.description,
								})
							)}
							value={{
								label: formMethods.watch(
									"business_details.address"
								),
								value: formMethods.watch(
									"business_details.address"
								),
							}}
							onTypingChange={(value) => {
								addressAutocomplete.handleSelectSuggestion(
									value
								);
								addressAutocomplete.setValue(value);
							}}
							onValueChange={(option) => {
								addressAutocomplete.handleSelectSuggestion(
									option.label
								);
							}}
							isLoading={addressAutocomplete.isLoading}
							emptyMessage={
								!addressAutocomplete.suggestions.length &&
								addressAutocomplete.value.length &&
								!addressAutocomplete.isLoading
									? "No address found"
									: "Enter an address"
							}
							autoComplete="new"
						/>
						{formMethods.formState.errors.business_details
							?.address &&
							(
								formMethods.formState.errors.business_details
									?.address?.message as string
							)?.length && (
								<small className="text-xs leading-[16px] text-[#c9312c]">
									{
										formMethods.formState.errors
											.business_details.address
											.message as string
									}
								</small>
							)}
					</div>

					<div className="flex flex-1 flex-col space-y-1.5">
						<Label className="text-[#323539]">Zip Code</Label>

						<Input
							className="m-0"
							{...formMethods.register(
								"business_details.zip_code"
							)}
						/>
						{formMethods.formState.errors.business_details?.zip_code
							?.message && (
							<small className="text-sm text-[#c9312c]">
								{
									formMethods.formState.errors
										.business_details.zip_code
										?.message as string
								}
							</small>
						)}
					</div>
				</div>
				<div className="flex justify-between space-x-7">
					<div className="flex flex-1 flex-col space-y-1.5">
						<Label className="text-[#323539]">
							Country <span className="text-[#c9312c]">*</span>
						</Label>

						<Select
							value={formMethods.watch(
								"business_details.country"
							)}
							onValueChange={(value) => {
								updateCountryAndState(
									formMethods.setValue,
									setProvinceOptions,
									false,
									undefined,
									value
								);
							}}
						>
							<SelectTrigger className="shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]">
								<SelectValue className="text-[#323539]" />
							</SelectTrigger>
							<SelectContent className="!z-[9999]">
								<SelectGroup>
									<SelectLabel className="px-2">
										Province
									</SelectLabel>

									{countryOptions.map((option) => {
										return (
											<SelectItem
												key={option.value}
												value={option.value}
												className="px-8"
											>
												{option.label}
											</SelectItem>
										);
									})}
								</SelectGroup>
							</SelectContent>
						</Select>
						{formMethods.formState.errors.business_details
							?.country &&
							(
								formMethods.formState.errors.business_details
									?.country?.message as string
							)?.length && (
								<small className="mt-1.5 text-xs leading-[16px] text-[#c9312c]">
									{
										formMethods.formState.errors
											.business_details.country
											.message as string
									}
								</small>
							)}
					</div>
					<div className="flex flex-1 flex-col space-y-1.5">
						<Label className="text-[#323539]">
							State <span className="text-[#c9312c]">*</span>
						</Label>
						<Select
							value={formMethods.watch("business_details.state")}
							onValueChange={(value) => {
								updateCountryAndState(
									formMethods.setValue,
									setProvinceOptions,
									false,
									value,
									formMethods.getValues(
										"business_details.country"
									)
								);
							}}
						>
							<SelectTrigger className="shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]">
								<SelectValue className="text-[#323539]" />
							</SelectTrigger>
							<SelectContent className="!z-[9999]">
								<SelectGroup>
									<SelectLabel className="px-2">
										Province
									</SelectLabel>

									{provinceOptions.map((option) => {
										return (
											<SelectItem
												key={option.value}
												value={option.value}
												className="px-8"
											>
												{option.label}
											</SelectItem>
										);
									})}
								</SelectGroup>
							</SelectContent>
						</Select>
						{formMethods.formState.errors.business_details?.state &&
							(
								formMethods.formState.errors.business_details
									.state.message as string
							)?.length && (
								<small className="mt-1.5 text-xs leading-[16px] text-[#c9312c]">
									{
										formMethods.formState.errors
											.business_details.state
											.message as string
									}
								</small>
							)}
					</div>
					<div className="flex flex-1 flex-col space-y-1.5">
						<Label className="text-[#323539]">
							City <span className="text-[#c9312c]">*</span>
						</Label>
						<Input
							className="border border-[#E4E4E7] shadow-[0px_1px_2px_0px_rgba(16,24,40,0.05)]"
							autoComplete="new"
							{...formMethods.register("business_details.city")}
						/>
						{formMethods.formState.errors.business_details?.city &&
							(
								formMethods.formState.errors.business_details
									?.city?.message as string
							)?.length && (
								<small className="mt-1.5 text-xs leading-[16px] text-[#c9312c]">
									{
										formMethods.formState.errors
											.business_details?.city
											?.message as string
									}
								</small>
							)}
					</div>
				</div>
				<div className="flex flex-col space-y-1.5">
					<Label className="block text-sm font-medium tracking-[-0.1px] text-[#323539]">
						Logo
					</Label>
					<div
						className="flex w-full cursor-pointer items-center space-x-2 rounded-md border border-[#E5E5E7] bg-white px-3 py-2 text-[#323539] placeholder:text-[#323539]/50"
						onClick={() => {
							logoRef.current.click();
						}}
					>
						<input
							type="file"
							ref={logoRef}
							onChange={handleFileChange}
							className="hidden"
							accept=".png,.jpg,.jpeg,.svg"
						/>
						<Button
							className="size-fit rounded-[4px] px-2.5 py-0.5 text-sm font-medium tracking-[-0.1px] text-white"
							type="button"
							disabled={false}
						>
							Choose File
						</Button>

						<p>{logoFile && logoFile.name}</p>
					</div>
					{formMethods.formState.errors.business_details?.logo &&
						(
							formMethods.formState.errors.business_details?.logo
								?.message as string
						)?.length && (
							<small className="mt-1.5 text-xs leading-[16px] text-[#c9312c]">
								{
									formMethods.formState.errors
										.business_details?.logo
										?.message as string
								}
							</small>
						)}
				</div>
			</div>

			<div className="flex items-center justify-end space-x-8 rounded-b-[10px] bg-[#FAFBFC] px-8 py-4">
				<Button
					className={cn(
						"relative h-10 max-w-[103px] flex-1 rounded-md text-white duration-200 ease-in-out"
						// {
						// 	"bg-[#E5E5E7] text-[#323539] hover:bg-[#E5E5E7]":
						// 		!isValid,
						// 	"bg-primary text-white": isValid,
						// }
					)}
					onClick={handleSubmit}
					type="button"
					disabled={NewOnboardingImageUploadMutation.isPending}
				>
					Next
				</Button>
			</div>
		</div>
	);
};

export default AboutBusiness;
