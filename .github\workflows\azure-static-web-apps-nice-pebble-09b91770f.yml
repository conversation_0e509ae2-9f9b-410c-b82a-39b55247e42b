name: Azure Static Web Apps CI/CD

on:
    push:
        branches:
            - main
            - dev
    pull_request:
        types: [opened, synchronize, reopened, closed]
        branches:
            - dev2

jobs:
    build_and_deploy_job:
        if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
        runs-on: ubuntu-latest
        environment:
            name: ${{ github.event_name == 'pull_request' && github.event.action != 'closed' && (github.ref == 'refs/heads/dev' && 'development' || github.ref == 'refs/heads/staging' && 'staging') || github.event_name == 'push' && (github.ref == 'refs/heads/dev' && 'development' || github.ref == 'refs/heads/staging' && 'staging') || github.event_name == 'push' && github.ref == 'refs/heads/main' && 'production' }}

        # environment: development
        env:
            NODE_OPTIONS: --max_old_space_size=4096
            NEXT_PUBLIC_ENVIRONMENT: "${{secrets.NEXT_PUBLIC_ENVIRONMENT}}"
            NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_ID: "${{ secrets.NEXT_PUBLIC_GOOGLE_OAUTH_CLIENT_ID }}"
            NEXT_PUBLIC_GOOGLE_OAUTH_PROVIDER_CLIENT_ID: "${{ secrets.NEXT_PUBLIC_GOOGLE_OAUTH_PROVIDER_CLIENT_ID }}"
            NEXT_PUBLIC_GOOGLE_OAUTH_MAPS_KEY: "${{ secrets.NEXT_PUBLIC_GOOGLE_OAUTH_MAPS_KEY }}"
            NEXT_PUBLIC_HELP_CRUNCH_APP_ID: "${{ secrets.NEXT_PUBLIC_HELP_CRUNCH_APP_ID }}"
            NEXT_PUBLIC_ORGANISATION: "${{ secrets.NEXT_PUBLIC_ORGANISATION }}"
            NEXT_PUBLIC_ORGANISATION_PROJECT: "${{ secrets.NEXT_PUBLIC_ORGANISATION_PROJECT }}"
            NEXT_PUBLIC_BASE_API_URL: "${{ secrets.NEXT_PUBLIC_BASE_API_URL }}"
            NEXT_PUBLIC_BASE_API_VERSION: "${{ secrets.NEXT_PUBLIC_BASE_API_VERSION }}"
            NEXT_PUBLIC_CLIENT_URL: "${{ secrets.NEXT_PUBLIC_CLIENT_URL }}"
            NEXT_PUBLIC_AZURE_TENANT_ID: "${{ secrets.NEXT_PUBLIC_AZURE_TENANT_ID }}"
            NEXT_PUBLIC_MICROSOFT_CLIENT_ID: "${{ secrets.NEXT_PUBLIC_MICROSOFT_CLIENT_ID }}"
        name: Build and Deploy Job
        steps:
            - uses: actions/checkout@v3
              with:
                  submodules: true
                  lfs: false
            - name: Build And Deploy
              id: builddeploy
              uses: Azure/static-web-apps-deploy@v1
              with:
                  production_branch: main
                  azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_NICE_PEBBLE_09B91770F }}
                  repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
                  action: "upload"
                  ###### Repository/Build Configurations - These values can be configured to match your app requirements. ######
                  # For more information regarding Static Web App workflow configurations, please visit: https://aka.ms/swaworkflowconfig
                  app_location: "/" # App source code path
                  api_location: "" # Api source code path - optional
                  output_location: "" # Built app content directory - optional
                  ###### End of Repository/Build Configurations ######

    close_pull_request_job:
        if: github.event_name == 'pull_request' && github.event.action == 'closed'
        runs-on: ubuntu-latest
        name: Close Pull Request Job
        steps:
            - name: Close Pull Request
              id: closepullrequest
              uses: Azure/static-web-apps-deploy@v1
              with:
                  app_location: "/" # Specify the app source code path
                  azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_NICE_PEBBLE_09B91770F }}
                  action: "close"
