"use client";

import Navbar from "@/components/Home/Navbar";
import SignUpTeamCard from "@/components/Signup/SignUpTeamCard";
import { NextPage } from "next";
import { Suspense, useState } from "react";

const SignUpTeam: NextPage = () => {
	const [showBookADemo, setShowBookDemo] = useState(false);
	const [showContactUsModal, setshowContactUsModal] = useState(false);
	return (
		<>
			<main className="relative flex h-[100dvh] flex-col">
				<Navbar
					isWhite
					showBookADemo={showBookADemo}
					setShowBookDemo={setShowBookDemo}
					setshowContactUsModal={setshowContactUsModal}
					showContactUsModal={showContactUsModal}
				/>
				<div className="clock-background-image flex w-full flex-1 items-center justify-center self-stretch px-4">
					<div className="relative flex w-full max-w-[1216px] items-center gap-12 mmd:flex-col-reverse mmd:py-12">
						<div className="z-10 flex flex-col space-y-3 md:space-y-6">
							<h1 className="text-2xl font-semibold tracking-[-2%] text-[#323539] md:text-[40px] md:font-semibold md:leading-[48px]">
								Join Organisation
							</h1>
							<p className="tracking-[-1%] text-[#323539] msm:text-lg">
								Create your account for streamlined scheduling,
								enhanced <br className="msm:hidden" />
								customer management and advanced analytics.
							</p>
						</div>
						<SignUpTeamCard />
					</div>
				</div>
			</main>
		</>
	);
};

const SignUpTeamWrapper: React.FC = () => {
	return (
		<Suspense fallback={<div />}>
			<SignUpTeam />
		</Suspense>
	);
};

export default SignUpTeamWrapper;
