export interface AuthMethod {
	type: "google" | "microsoft" | "sso" | "password";
	icon?: string;
	label?: string;
}

export interface TenantTheme {
	primaryColor: string;
	secondaryColor?: string;
	backgroundColor?: string;
	textColor?: string;
}

export interface TenantConfig {
	id: string;
	name: string;
	subdomain: string;
	logo: string | null;
	authMethods: AuthMethod[];
	theme: TenantTheme;
	redirectAfterLogin?: string;
	showMicrosoftLogin?: boolean;
	showGoogleLogin?: boolean;
	showSSOLogin?: boolean;
	ssoLoginUrl?: string;
	// Tenant-specific login settings
	loginType: "standard" | "custom";
	customLoginMessage?: string;
	ssoButtonText?: string;
	showPasswordLogin: boolean;
}
