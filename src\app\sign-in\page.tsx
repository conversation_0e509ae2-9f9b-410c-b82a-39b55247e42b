"use client";

import { Suspense, useEffect, useState } from "react";
import { NextPage } from "next";
import LoginRouter from "@/components/auth/LoginRouter";
import Navbar from "@/components/Home/Navbar";
import { tenantConfigs } from "@/utils/services/tenantService";
import Loader from "@/components/Loader";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { AuthLoginResponse } from "@/types/sign-in";
import { useHandleSSO } from "@/hooks/useHandleSSO";
import UserNotFoundModal from "@/components/UserNotFoundModal";
import { useSearchParams } from "next/navigation";
import { APIVersion2SSOUserVerify } from "@/http/v2";
// import { Divide } from "lucide-react";

const SignInContent = () => {
	const [showUserNotFoundError, setShowUserNotFoundError] = useState(false);
	const searchParams = useSearchParams();
	const handleLoginSuccess = useHandleLoginSuccess();
	const { tenantConfig, isLoading, isError, shouldVerifySSO } = useHandleSSO(
		tenantConfigs["default"]
	);

	useEffect(() => {
		const handleAuthToken = async () => {
			const token = searchParams.get("auth");
			const error = searchParams.get("error");

			if (token) {
				try {
					const response = await APIVersion2SSOUserVerify(token);
					const userData = response as AuthLoginResponse;
					if (userData.status && userData.data) {
						if (!userData.data.is_email_verified) {
							setShowUserNotFoundError(true);
							return;
						}
						return handleLoginSuccess(userData);
					}
				} catch (error) {
					setShowUserNotFoundError(true);
				}
			} else if (error === "user_not_found") {
				setShowUserNotFoundError(true);
			}
		};

		handleAuthToken();
	}, [searchParams]);

	// todo: test this scenerio for height decrepancies
	return (
		<>
			<div className="clock-background-image flex w-full flex-1 items-center justify-center self-stretch px-4">
				{shouldVerifySSO && isLoading ? (
					<div className="flex h-[100dvh] items-center justify-center">
						<Loader />
					</div>
				) : shouldVerifySSO && isError ? (
					<div className="text-center">
						Error loading tenant configuration
					</div>
				) : (
					<LoginRouter tenantConfig={tenantConfig} />
				)}
			</div>
			{showUserNotFoundError && (
				<UserNotFoundModal
					show={showUserNotFoundError}
					setShow={setShowUserNotFoundError}
					handleUserNotFoundError={() => {
						setShowUserNotFoundError(false);
						window.location.href = "/sign-in";
					}}
				/>
			)}
		</>
	);
};

const Home: NextPage = () => {
	const [showBookADemo, setShowBookDemo] = useState(false);
	const [showContactUsModal, setshowContactUsModal] = useState(false);

	return (
		<main className="relative flex h-[100dvh] flex-col">
			<Navbar
				isWhite
				showBookADemo={showBookADemo}
				setShowBookDemo={setShowBookDemo}
				setshowContactUsModal={setshowContactUsModal}
				showContactUsModal={showContactUsModal}
			/>
			<Suspense
				fallback={
					<div className="flex h-[80vh] items-center justify-center">
						<Loader />
					</div>
				}
			>
				<SignInContent />
			</Suspense>
		</main>
	);
};

export default Home;
