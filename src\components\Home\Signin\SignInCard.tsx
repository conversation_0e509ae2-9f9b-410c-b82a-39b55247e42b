"use client";

import VerifyEmail from "@/components/Onboarding/VerifyEmail";
import RequestIsLoading from "@/components/RequestIsLoading";
import SignInWithMicrosoft from "@/components/Signup/azure/SignInWithMicrosoft";
import CreateAccountOnSSO from "@/components/Signup/CreateAccountOnSSO";
import SigninWIthGoogle from "@/components/Signup/google/SigninWIthGoogle";
import Checkbox from "@/components/ui-extended/checkbox";
import InputIcon from "@/components/ui-extended/input-icon";
import { LoaderButton } from "@/components/ui-extended/loader-button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { useLoginUser } from "@/store/slices/auth";
import { GoogleOauthLoginSlice } from "@/store/slices/signin/googleOauthLoginSlice";
import useUserStore from "@/store/useUserStore";
import { UserLoginSchema, UserLoginType } from "@/types/signup";
import { LANDING_ENVIRONMENT_LINK } from "@/utils/constants";
import { zodResolver } from "@hookform/resolvers/zod";
import { Key } from "lucide-react";
import Link from "next/link";
import React from "react";
import { FormProvider, SubmitHandler, useForm } from "react-hook-form";
import { LuEye, LuEyeOff } from "react-icons/lu";

interface SignInCardProps {
	onSwitchToSSO?: () => void;
}

const SignInCard: React.FC<SignInCardProps> = ({ onSwitchToSSO }) => {
	const [passwordType, setPasswordType] = React.useState("password");
	const { setMfaUser, setUser, rememberAuth, setRememberAuth } = useUserStore(
		(s) => ({
			rememberAuth: s.rememberAuth,
			setRememberAuth: s.setRememberAuth,
			setMfaUser: s.setMfaUser,
			setUser: s.setUser,
			reset: s.reset,
		})
	);
	const [showVerifyEmailModal, setShowVerifyEmailModal] =
		React.useState(false);
	const [showCreateAccountOnSSO, setShowCreateAccountOnSSO] =
		React.useState(false);
	const [SSOToken, setSSOToken] = React.useState<{
		type: "microsoft" | "google";
		token: string | null;
	} | null>(null);

	const formMethods = useForm<UserLoginType>({
		resolver: zodResolver(UserLoginSchema),
	});

	const loginUserMutaion = useLoginUser(formMethods.setError);
	const handleLoginSuccess = useHandleLoginSuccess();
	const googleOauthLoginMutation = GoogleOauthLoginSlice();

	const onSubmit: SubmitHandler<UserLoginType> = async (data) => {
		try {
			setMfaUser(null);
			setUser(null);
			loginUserMutaion.mutate(
				{
					...data,
					remember_me: rememberAuth?.rememberMe ?? false,
					remember_token:
						rememberAuth?.rememberToken &&
						rememberAuth?.rememberToken,
				},
				{
					onSuccess: (data) => {
						handleLoginSuccess(data);
						setShowVerifyEmailModal(
							Boolean(
								!("twoFactor" in data.data) &&
									!data.data.is_email_verified
							)
						);
					},
				}
			);
		} catch (error) {
			formMethods.setError("root", {
				message: "An error occured kindly try again later",
			});
		}
	};

	React.useEffect(() => {
		setRememberAuth({ ...rememberAuth });
	}, []);

	return (
		<div className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]">
			<div className="flex flex-col space-y-2 px-8">
				<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
					Sign in to my account
				</h3>
				<a
					href={LANDING_ENVIRONMENT_LINK + "/sign-up"}
					className="font-normal tracking-[-1%] text-[#858C95]"
				>
					Don&apos;t have an account?{" "}
					<span className="text-[#195388]">Sign up</span>
				</a>
			</div>
			<FormProvider {...formMethods}>
				<form
					onSubmit={formMethods.handleSubmit(onSubmit)}
					className="flex flex-col space-y-6"
				>
					<div className="flex flex-col space-y-6 px-8">
						<div className="space-y-1.5">
							<Label htmlFor="email" className="text-[#323539]">
								Email Address{" "}
								<span className="text-[#c9312c]">*</span>
							</Label>
							<Input
								id="email"
								max={254}
								{...formMethods.register("email")}
								aria-describedby={
									formMethods.formState.errors.email
										? "emailError"
										: undefined
								}
								placeholder="Enter your email address"
							/>
							{formMethods.formState.errors.email?.message && (
								<small
									id="emailError"
									role="alert"
									className="mt-1.5 text-sm text-[#c9312c]"
								>
									{
										formMethods.formState.errors.email
											?.message
									}
								</small>
							)}
						</div>
						<div className="space-y-1.5">
							<Label
								htmlFor="password"
								className="text-[#323539]"
							>
								Password{" "}
								<span className="text-[#c9312c]">*</span>
							</Label>
							<InputIcon
								id="password"
								type={passwordType}
								{...formMethods.register("password")}
								aria-describedby={
									formMethods.formState.errors.password
										? "passwordHelp passwordError"
										: "passwordHelp"
								}
								outerClassName="flex flex-row-reverse"
								placeholder="Enter your password"
								bigIcon={
									passwordType === "password" ? (
										<LuEyeOff
											onClick={() =>
												setPasswordType(
													passwordType === "password"
														? "text"
														: "password"
												)
											}
											className="cursor-pointer text-main-1"
										/>
									) : (
										<LuEye
											onClick={() =>
												setPasswordType(
													passwordType === "password"
														? "text"
														: "password"
												)
											}
											className="cursor-pointer text-main-1"
										/>
									)
								}
							/>

							{formMethods.formState.errors.password?.message && (
								<small
									id="passwordError"
									role="alert"
									className="text-sm text-[#c9312c]"
								>
									{
										formMethods.formState.errors.password
											?.message
									}
								</small>
							)}
						</div>
					</div>
					{formMethods.formState.errors.root?.message && (
						<p className="px-8 text-sm tracking-[-0.1px] text-red-500">
							{formMethods.formState.errors.root?.message}
						</p>
					)}
					<div className="flex items-center justify-between space-x-3 px-8">
						<div
							className="flex items-center space-x-1.5"
							onClick={() =>
								setRememberAuth({
									...rememberAuth,
									rememberMe: !rememberAuth?.rememberMe,
								})
							}
						>
							<Checkbox
								handleCheckboxChange={() =>
									setRememberAuth({
										...rememberAuth,
										rememberMe: !rememberAuth?.rememberMe,
									})
								}
								isChecked={rememberAuth?.rememberMe}
								id="remember-me"
								className="h-4 w-4 rounded-sm border-[#323539]"
							/>
							{rememberAuth?.rememberMe}
							<Label>Remember me for the next 30 days</Label>
						</div>
						<Link
							href={"/forgot-password"}
							className="font-normal tracking-[-1%] underline"
						>
							Forgot Password?
						</Link>
					</div>

					<div className="flex flex-col items-stretch space-y-4 bg-[#FAFBFC] px-8 pb-4 pt-[18px]">
						<LoaderButton
							disabled={loginUserMutaion.isPending}
							loading={loginUserMutaion.isPending}
							loaderSize={20}
							className="h-10 w-full text-white"
							type="submit"
						>
							Sign in
						</LoaderButton>
						<p className="text-center text-sm text-[#858C95]">
							Or Sign in With
						</p>
						<div className="flex sm:space-x-2 msm:flex-col msm:space-y-2">
							<SigninWIthGoogle
								type="sign-in"
								setSSOToken={setSSOToken}
								setShowCreateAccountOnSSO={
									setShowCreateAccountOnSSO
								}
							/>
							<SignInWithMicrosoft
								type="sign-in"
								setSSOToken={setSSOToken}
								setShowCreateAccountOnSSO={
									setShowCreateAccountOnSSO
								}
							/>
							<button
								type="button"
								onClick={onSwitchToSSO}
								className="flex h-10 w-full items-center justify-center gap-x-2 rounded-md border border-[#E9EAEB] bg-white px-3 text-sm font-medium text-[#323539] hover:bg-gray-50 md:w-fit"
							>
								<Key
									size={14}
									className="rotate-[-270] scale-x-[-1]"
								/>
								SSO
							</button>
						</div>
					</div>
				</form>
			</FormProvider>
			<RequestIsLoading
				isWhite
				size={20}
				isLoading={googleOauthLoginMutation.isPending}
			/>
			<VerifyEmail
				email={formMethods.watch("email")}
				showVerifyEmailModal={showVerifyEmailModal}
				setShowVerifyEmailModal={setShowVerifyEmailModal}
			/>
			<CreateAccountOnSSO
				SSOToken={SSOToken}
				showCreateAccountOnSSO={showCreateAccountOnSSO}
				setShowCreateAccountOnSSO={setShowCreateAccountOnSSO}
			/>
		</div>
	);
};

export default SignInCard;
