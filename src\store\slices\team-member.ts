import {
	APIVersion2TeamMemberAccept,
	APIVersion2TeamMemberVerifyInvitation,
} from "@/http/v2";
import {
	ForgotPasswordErrorResponse,
	ResetPasswordDataErrorResponse,
} from "@/types/forgot-reset";
import { AuthLoginResponse } from "@/types/sign-in";
import {
	AcceptTeamMemberErrorNoParamsResponse,
	AcceptTeamMemberErrorResponse,
	AcceptTeamMemberResponse,
	AcceptTeamMemberType,
} from "@/types/team-member";
import { useMutation, useQuery } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const VerifyTeamMemberInvitationSlice = (token: string | null) => {
	return useQuery<unknown, AxiosError<ForgotPasswordErrorResponse>>({
		queryFn: () => APIVersion2TeamMemberVerifyInvitation({ token }),
		queryKey: ["verify-team-member-invitation", token],
		enabled: Boolean(token),
	});
};

export const AcceptTeamMemberInvitationSlice = () => {
	return useMutation<
		AuthLoginResponse,
		AxiosError<
			| AcceptTeamMemberErrorResponse
			| AcceptTeamMemberErrorNoParamsResponse
		>,
		AcceptTeamMemberType
	>({
		mutationFn: APIVersion2TeamMemberAccept,
	});
};
