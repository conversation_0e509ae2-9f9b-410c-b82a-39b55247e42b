"use client";

import { ForgotParams, ForgotSchema } from "@/types/forgot-reset";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter } from "next/navigation";
import React from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { Label } from "../ui/label";
import { Input } from "../ui/input";
import { LoaderButton } from "../ui-extended/loader-button";
import { useForgotPasswordSlice } from "@/store/slices/forgetResetPassword";

const ForgotPasswordCard: React.FC = () => {
	const {
		register,
		handleSubmit,
		// setError,
		formState: { errors },
	} = useForm<ForgotSchema>({
		resolver: zodResolver(ForgotParams),
	});
	const router = useRouter();

	const forgetPasswordMutation = useForgotPasswordSlice();

	const onSubmit: SubmitHandler<ForgotSchema> = async (data) => {
		forgetPasswordMutation.mutate(data, {
			onSuccess: () =>
				setTimeout(() => {
					router.push("/sign-in");
				}, 10000),
		});
	};

	return (
		<form
			className="z-10 flex w-full max-w-[488px] flex-col space-y-6 rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
			onSubmit={handleSubmit(onSubmit)}
		>
			<div className="flex flex-col space-y-2 px-4 md:px-8">
				<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
					Reset it in just a few steps.
				</h3>
				<p className="font-normal tracking-[-1%] text-[#858C95]">
					Enter your registered email to reset your password.
				</p>
			</div>
			<div className="flex flex-col space-y-1.5 px-4 md:px-8">
				<Label htmlFor="confirmPassword" className="text-[#323539]">
					Email Address <span className="text-[#c9312c]">*</span>
				</Label>
				<Input
					id="email"
					type="text"
					max={254}
					{...register("email")}
					aria-describedby={errors.email ? "emailError" : undefined}
				/>
				{forgetPasswordMutation.isSuccess && (
					<p className="text-sm tracking-[-0.1px] text-[#48AA75]">
						Instructions to reset your account has been sent to your
						email
					</p>
				)}
				{forgetPasswordMutation.isError && (
					<p className="text-sm tracking-[-0.1px] text-destructive">
						Email could not be sent. Try again later 🤕
					</p>
				)}
				{errors.email?.message && (
					<small
						id="emailError"
						role="alert"
						className="mt-1.5 text-sm text-[#c9312c]"
					>
						{errors.email?.message}
					</small>
				)}
			</div>
			<div className="rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8">
				<LoaderButton
					disabled={forgetPasswordMutation.isPending}
					loaderSize={20}
					loading={forgetPasswordMutation.isPending}
					className={
						"h-10 w-fit overflow-hidden text-white md:h-10 mmd:text-[15px]"
					}
					type="submit"
				>
					Reset Password
				</LoaderButton>
			</div>
		</form>
	);
};

export default ForgotPasswordCard;
