import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import {
	APIVersion2Get2faSettings,
	APIVersion2ManualVerify2faAppOTP,
	APIVersion2SendCodeToEmail,
	APIVersion2Skip2fa,
	APIVersion2Verify2faAppOTP,
} from "@/http/v2";
import {
	GetMFASettingsResponse,
	ManualVerifyApp2FAErrorResponse,
	ManualVerifyApp2FAProps,
	ManualVerifyApp2FAResponse,
	SendCodeToEmailProps,
	VerifyApp2FAErrorReponse,
	VerifyApp2FAProps,
} from "@/types/mfa";
import { AuthLoginResponse, AuthSendOTPToEmailResponse } from "@/types/sign-in";
import { User } from "@/types/signup";
import { useQuery, useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const Get2faSettingsSlice = (enabled?: boolean) => {
	const get2faSettingsQuery = useQuery<GetMFASettingsResponse, Error>({
		queryKey: ["get-2fa-settings"],
		queryFn: APIVersion2Get2faSettings,
		enabled,
		retry: 0,
		refetchOnMount: false,
		refetchOnWindowFocus: false,
		refetchOnReconnect: false,
		staleTime: Infinity,
		gcTime: Infinity,
	});

	return get2faSettingsQuery;
};

export const Verify2faAppOTPSlice = (
	onSuccess: (data: AuthLoginResponse) => void = () => {
		return;
	},
	onError: (error: AxiosError<any>) => void = () => {
		return;
	}
) => {
	const handleLoginSuccess = useHandleLoginSuccess();
	return useMutation<
		AuthLoginResponse,
		AxiosError<VerifyApp2FAErrorReponse>,
		VerifyApp2FAProps
	>({
		mutationFn: APIVersion2Verify2faAppOTP,
		onSuccess: (data) => {
			onSuccess(data);
			handleLoginSuccess(data);
		},
		onError: (error) => {
			onError(error);

			console.error(error);
		},
	});
};

export const SendCodeToEmailSlice = (
	onSuccess: (data: AuthSendOTPToEmailResponse) => void = () => {
		return;
	},
	onError: (error: AxiosError<any>) => void = () => {
		return;
	}
) => {
	return useMutation<
		AuthSendOTPToEmailResponse,
		AxiosError<VerifyApp2FAErrorReponse>,
		SendCodeToEmailProps
	>({
		mutationFn: APIVersion2SendCodeToEmail,
		onSuccess: (data) => {
			onSuccess(data);
		},
		onError: (error) => {
			onError(error);
		},
	});
};

export const ManualVerify2faAppOTPSlice = () => {
	return useMutation<
		ManualVerifyApp2FAResponse,
		AxiosError<ManualVerifyApp2FAErrorResponse>,
		ManualVerifyApp2FAProps
	>({
		mutationFn: APIVersion2ManualVerify2faAppOTP,
		onError: (error) => {
			console.error(error);
		},
	});
};

export const Skip2faOTPSlice = () => {
	const handleLoginSuccess = useHandleLoginSuccess();
	return useMutation<
		ManualVerifyApp2FAResponse,
		AxiosError<ManualVerifyApp2FAErrorResponse>
	>({
		mutationFn: APIVersion2Skip2fa,
		onSuccess: handleLoginSuccess,
		onError: (error) => {
			console.error(error);
		},
	});
};
