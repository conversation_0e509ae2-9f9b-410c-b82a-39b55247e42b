import { useGoogleLogin } from "@react-oauth/google";
import React from "react";
import useCustomToast from "../../CustomToast";
import { But<PERSON> } from "../../ui/button";
import { GoogleOauthLoginSlice } from "@/store/slices/signin/googleOauthLoginSlice";
import { LoaderButton } from "@/components/ui-extended/loader-button";
import { StringToBoolean } from "class-variance-authority/types";

export type GoogleAuthType = "sign-in" | "sign-up";

const SigninWIthGoogle: React.FC<{
	type: GoogleAuthType;
	setSSOToken?: React.Dispatch<
		React.SetStateAction<{
			type: "microsoft" | "google";
			token: string | null;
		} | null>
	>;
	setShowCreateAccountOnSSO?: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ type, setShowCreateAccountOnSSO, setSSOToken }) => {
	const customToast = useCustomToast();
	const googleOauthLoginMutation = GoogleOauthLoginSlice();

	const errorMessage = () => {
		customToast("Google sign up failed", {
			id: "google-signup",
			type: "success",
		});
	};

	const initiateGoogleSignup = useGoogleLogin({
		onError: errorMessage,

		onSuccess: async (codeResponse) => {
			setSSOToken?.({ type: "google", token: codeResponse.access_token });
			googleOauthLoginMutation.mutate(
				{
					token: codeResponse.access_token,
					type: type,
				},
				{
					onError: (error) => {
						if (error.response?.data.error === "User not found") {
							customToast("This user does not exist", {
								id: "microsoft-signup",
								type: "error",
							});
							setShowCreateAccountOnSSO?.(true);
						}
						if (
							error?.response?.data?.error ==
							"User already exists"
						) {
							return customToast("This user already exists", {
								id: "microsoft-signup",
								type: "error",
							});
						}
					},
				}
			);
		},
		scope: "openid profile email https://www.googleapis.com/auth/calendar",
	});

	return (
		<LoaderButton
			onClick={() => initiateGoogleSignup()}
			type="button"
			variant="outline"
			className="flex-1"
			loaderSize={20}
			disabled={googleOauthLoginMutation.isPending}
			loading={googleOauthLoginMutation.isPending}
		>
			{" "}
			<div className="flex flex-1 space-x-2">
				<img
					className="size-[18px]"
					src="/assets/icons/signin/google.svg"
					alt="google signup"
				/>
				<span>Google</span>{" "}
			</div>
		</LoaderButton>
	);
};

export default SigninWIthGoogle;
