import { APIVersion1ForgotPassword, APIVersion1ResetPassword } from "@/http/v1";
import {
	ForgotPasswordErrorResponse,
	ForgotPasswordResponse,
	ForgotSchema,
	ResetPasswordDataErrorResponse,
	ResetSchema,
} from "@/types/forgot-reset";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const useForgotPasswordSlice = () => {
	return useMutation<
		ForgotPasswordResponse,
		AxiosError<ForgotPasswordErrorResponse>,
		ForgotSchema
	>({
		mutationFn: APIVersion1ForgotPassword,
	});
};

export const useResetPassword = () => {
	return useMutation<
		unknown,
		AxiosError<ResetPasswordDataErrorResponse>,
		ResetSchema
	>({
		mutationFn: APIVersion1ResetPassword,
	});
};
