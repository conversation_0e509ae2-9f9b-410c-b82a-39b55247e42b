import useCustomToast from "@/components/CustomToast";
import { LoaderButton } from "@/components/ui-extended/loader-button";
import { CONFIGS } from "@/configs";
import {
	MicrosoftOatuhLoginSlice,
	MicrosoftOatuhRegisterSlice,
} from "@/store/slices/signin/microsoftOatuhLoginSlice";
import * as msal from "@azure/msal-browser";
import React, { useEffect, useState } from "react";

// Initialize MSAL instance
const msalInstance = new msal.PublicClientApplication({
	auth: {
		clientId: CONFIGS.MICROSOFT.CLIENT_ID as string,
		authority: `https://login.microsoftonline.com/${CONFIGS.MICROSOFT.TENANT_ID}`,
		redirectUri: CONFIGS.URL.CLIENT_URL + "/sign-in",
	},
});

const SignInWithMicrosoft: React.FC<{
	type: "sign-in" | "sign-up";
	setSSOToken?: React.Dispatch<
		React.SetStateAction<{
			type: "microsoft" | "google";
			token: string | null;
		} | null>
	>;
	setShowCreateAccountOnSSO?: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ type, setSSOToken, setShowCreateAccountOnSSO }) => {
	const [isMsalInitialized, setIsMsalInitialized] = useState(false);
	const customToast = useCustomToast();
	const microsoftLoginMutation = MicrosoftOatuhLoginSlice();
	const microsoftRegisterMutation = MicrosoftOatuhRegisterSlice();

	useEffect(() => {
		const initializeMsal = async () => {
			await msalInstance.initialize();
			setIsMsalInitialized(true);
		};
		initializeMsal();
	}, []);

	const loginWithMicrosoft = async () => {
		if (!isMsalInitialized) return;

		try {
			const response = await msalInstance.loginPopup({
				scopes: ["openid", "profile", "email", "User.Read"],
			});
			setSSOToken?.({ type: "microsoft", token: response.accessToken });
			if (type === "sign-in")
				microsoftLoginMutation.mutate(
					{ token: response.accessToken },
					{
						onError: (error) => {
							{
								if (
									error.response?.data.error ===
									"User not found"
								) {
									customToast("This user does not exist", {
										id: "microsoft-signup",
										type: "error",
									});
									setShowCreateAccountOnSSO?.(true);
								}
								if (
									error?.response?.data?.error ==
									"User already exists"
								) {
									return customToast(
										"This user already exists",
										{
											id: "microsoft-signup",
											type: "error",
										}
									);
								}
							}
						},
					}
				);
			else if (type === "sign-up")
				microsoftRegisterMutation.mutate(
					{
						token: response.accessToken,
					},
					{
						onError: (error) => {
							if (
								error.response?.data.error === "User not found"
							) {
								customToast("This user does not exist", {
									id: "microsoft-signup",
									type: "error",
								});
								setShowCreateAccountOnSSO?.(true);
							}
							if (
								error?.response?.data?.error ==
								"User already exists"
							) {
								return customToast("This user already exists", {
									id: "microsoft-signup",
									type: "error",
								});
							}
						},
					}
				);
		} catch (error) {
			console.error("Microsoft login error:", error);
		}
	};

	return (
		<LoaderButton
			onClick={loginWithMicrosoft}
			type="button"
			variant="outline"
			className="flex-1"
			loaderSize={20}
			disabled={!isMsalInitialized || microsoftLoginMutation.isPending}
			loading={microsoftLoginMutation.isPending}
		>
			<div className="flex flex-1 space-x-2">
				<img
					className="size-[18px]"
					src="/assets/icons/signin/microsoft.svg"
					alt="microsoft login"
				/>
				<span>Microsoft</span>
			</div>
		</LoaderButton>
	);
};

export default SignInWithMicrosoft;
