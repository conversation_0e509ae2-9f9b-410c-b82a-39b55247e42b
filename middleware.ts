import { NextRequest, NextResponse } from "next/server";

export function middleware(request: NextRequest) {
	const hostname = request.headers.get("host") || "";
	const subdomain = hostname.split(".")[0];

	const requestHeaders = new Headers(request.headers);
	requestHeaders.set("x-tenant-subdomain", subdomain);

	return NextResponse.next({
		request: {
			headers: requestHeaders,
		},
	});
}

export const config = {
	matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"],
};
