import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import {
	APIVersion2MicrosoftOauthLogin,
	APIVersion2MicrosoftOauthRegister,
} from "@/http/v2";
import { AuthLoginResponse } from "@/types/sign-in";
import { AuthTwoEnabledFactorResponse, User } from "@/types/signup";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const MicrosoftOatuhLoginSlice = () => {
	const handleLoginSuccess = useHandleLoginSuccess();

	return useMutation<
		AuthLoginResponse | AuthTwoEnabledFactorResponse,
		AxiosError<any>,
		{ token: string }
	>({
		mutationFn: APIVersion2MicrosoftOauthLogin,
		onSuccess: (data) => {
			handleLoginSuccess(data);
		},
	});
};

export const MicrosoftOatuhRegisterSlice = () => {
	const handleLoginSuccess = useHandleLoginSuccess();

	return useMutation<
		AuthLoginResponse | AuthTwoEnabledFactorResponse,
		AxiosError<any>,
		{ token: string }
	>({
		mutationFn: APIVersion2MicrosoftOauthRegister,
		onSuccess: (data) => {
			handleLoginSuccess(data);
		},
	});
};
