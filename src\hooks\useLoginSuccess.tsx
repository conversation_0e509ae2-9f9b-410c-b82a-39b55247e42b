import useCustomToast from "@/components/CustomToast";
import { AuthLoginResponse, AuthUserData } from "@/types/sign-in";
import { AuthTwoEnabledFactorResponse } from "@/types/signup";
import {
	SPACES_ENVIRONMENT_LINK,
	WAITLIST_ENVIRONMENT_LINK,
} from "@/utils/constants";
import { useRouter } from "next/navigation";
import { setCookie } from "../utils/cookies";
import useUserStore from "@/store/useUserStore";

export const useHandleLoginSuccess = () => {
	const router = useRouter();
	const customToast = useCustomToast();
	const { rememberAuth, setUser, setMfaUser, setRememberAuth } = useUserStore(
		(s) => ({
			rememberAuth: s.rememberAuth,
			setUser: s.setUser,
			setMfaUser: s.setMfaUser,
			setRememberAuth: s.setRememberAuth,
		})
	);

	return (data: AuthLoginResponse | AuthTwoEnabledFactorResponse) => {
		// for partner related (spaces) redirection
		if ((data as AuthLoginResponse).data.company_id) {
			const type = "partner";
			const token = data.data.token;
			const orgId = (data as AuthLoginResponse).data.company_id;
			customToast("Login successful 🎉", {
				id: "login",
			});
			return window.open(
				`${SPACES_ENVIRONMENT_LINK}/login?token=${token}&type=${type}&organization=${orgId}`,
				"_self"
			);
		}
		setCookie("ac-token", data.data.token, 7);
		setUser(data.data as AuthUserData);
		if ("twoFactor" in data.data) {
			setMfaUser(data as AuthTwoEnabledFactorResponse);
			setRememberAuth({
				rememberMe: rememberAuth?.rememberMe,
				rememberToken: data.data.token,
			});
			return router.push("/2fa/verify");
		} else {
			if (!data.data.is_email_verified) return;
			if (!data.data.businesses || !data.data.businesses.length) {
				router.push("/onboarding");
				return;
			}
			if (!data.data.two_factor_enable) {
				setMfaUser({
					status: true,
					message: "2FA is not enabled",
					data: {
						twoFactor: false,
						token: data.data.token,
						expires_in: 2592000000, // 30 days
					},
				});
				router.push("/2fa/verify");
			}

			setCookie("ac-token", data.data.token, 7);
			setUser(data.data);
			customToast("Login successful 🎉", {
				id: "login",
			});
			if (data.data.remember_token && rememberAuth?.rememberMe)
				setRememberAuth({
					rememberMe: rememberAuth?.rememberMe,
					rememberToken: data.data.remember_token,
				});
			if (data.data.businesses[0].products.includes("spaces")) {
				// get user and company_id which you use to know who to redirect
				const type = "admin";
				const token = data.data.token;
				const orgId = data.data.company_id
					? data.data.company_id
					: data.data.businesses[0]?.id;
				return window.open(
					`${SPACES_ENVIRONMENT_LINK}/auth?token=${token}&type=${type}&orgId=${orgId}`,
					"_self"
				);
			}

			if (data.data.businesses[0].products.includes("primary"))
				if (data.data.default_business_id) {
					// out an conditional for this
					const userData = data.data as AuthUserData;
					const defaultBusiness = data.data.businesses.find(
						(b) => b.id === userData.default_business_id
					);

					if (defaultBusiness?.sso_enable && defaultBusiness.domain) {
						const envBaseUrl = WAITLIST_ENVIRONMENT_LINK;
						const urlObj = new URL(envBaseUrl);
						const domainWithoutProtocol = defaultBusiness.domain
							.replace(/^https?:\/\//, "")
							.replace(/\/$/, "");
						const newHostname = `${domainWithoutProtocol}.${urlObj.hostname}`;
						const ssoUrl = `${urlObj.protocol}//${newHostname}${urlObj.pathname}auth?token=${data.data.token}`;

						return window.open(ssoUrl, "_self");
					} else {
						return window.open(
							`${WAITLIST_ENVIRONMENT_LINK}/auth?token=${data.data.token}`,
							"_self"
						);
					}
				}

			return window.open(
				`${WAITLIST_ENVIRONMENT_LINK}/auth?token=${data.data.token}`,
				"_self"
			);
		}
	};
};
