"use client";

import Navbar from "@/components/Home/Navbar";
import MFACard from "@/components/Home/Signin/MFA/MFACard";
import RequireAuth from "@/hooks/useRequireAuth";
import React, { Suspense } from "react";

const SetupMFA = () => {
	const [showBookADemo, setShowBookDemo] = React.useState(false);
	const [showContactUsModal, setshowContactUsModal] = React.useState(false);
	// if (import.meta.env.PROD)
	// 	window.location.href = "https://migranium.com/sign-in";

	return (
		<Suspense>
			<RequireAuth />
			<main className="relative flex h-[100dvh] flex-col bg-red-900">
				<Navbar
					isWhite
					showBookADemo={showBookADemo}
					setShowBookDemo={setShowBookDemo}
					showContactUsModal={showContactUsModal}
					setshowContactUsModal={setshowContactUsModal}
					showSignOut
				/>
				<div className="clock-background-image flex w-full flex-1 items-center justify-center self-stretch px-4">
					<div className="relative flex w-full max-w-[1216px] items-center justify-center gap-12 mmd:py-12">
						<MFACard />
						{/* <div className="z-10 flex flex-col space-y-3 md:space-y-6">
						<h1 className="text-2xl font-bold text-[#323539] md:text-[40px] md:font-semibold md:leading-[32px] md:tracking-[-1.5%]">
							Welcome Back to Migranium
						</h1>
						<p className="tracking-[-1%] text-[#858C95] sm:text-lg">
							Sign in to continue optimizing your operational
							efficiency <br className="msm:hidden" /> and
							enhancing customer experiences.
						</p>
					</div> */}
					</div>
				</div>
			</main>
		</Suspense>
	);
};

export default SetupMFA;
