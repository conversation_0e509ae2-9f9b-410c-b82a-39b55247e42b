"use client";

import useCustomToast from "@/components/CustomToast";
import Loader from "@/components/Loader";
import RequestIsLoading from "@/components/RequestIsLoading";
import { LoaderButton } from "@/components/ui-extended/loader-button";
import { Button } from "@/components/ui/button";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import {
	Get2faSettingsSlice,
	ManualVerify2faAppOTPSlice,
	SendCodeToEmailSlice,
	Skip2faOTPSlice,
	Verify2faAppOTPSlice,
} from "@/store/slices/signin/mfaSlice";
import useUserStore from "@/store/useUserStore";
import { partiallyUnmaskEmail } from "@/utils/general";
import { zodResolver } from "@hookform/resolvers/zod";
import React, { useEffect, useState } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import { IoIosArrowBack } from "react-icons/io";
import { z } from "zod";
import OTPInput from "../OTPInput";
import { VerifyApp2FAProps } from "@/types/mfa";
import { cn } from "@/lib/utils";

interface CreateOTPSchemaOptions {
	length?: number;
	acceptStrings?: boolean;
}

export const createOTPSchema = ({
	length = 6,
	acceptStrings = false,
}: CreateOTPSchemaOptions = {}) => {
	let otpSchema = z
		.string()
		.min(length, `OTP must be ${length} digits`)
		.max(length, `OTP must be ${length} digits`);

	if (!acceptStrings) {
		otpSchema = otpSchema.regex(
			/^[0-9]+$/,
			"OTP must contain only numbers"
		);
	}

	return z.object({
		otp: otpSchema,
	});
};

export type OTPFormValues = z.infer<ReturnType<typeof createOTPSchema>>;

const MFACard: React.FC = () => {
	const skip2faMutation = Skip2faOTPSlice();
	const [currentScreen, setCurrentScreen] = useState<string>("default");
	const customToast = useCustomToast();
	const { user, rememberAuth, mfaUser, resetMfaUser } = useUserStore((s) => ({
		user: s.user,
		mfaUser: s.mfaUser,
		resetMfaUser: s.resetMfaUser,
		rememberAuth: s.rememberAuth,
	}));
	const [userData, setUserData] = useState<any>({});
	const [attemptCount, setAttemptCount] = useState(0);
	const [recoveryCodes, setRecoveryCodes] = useState<string[]>([]);
	const get2faSettingsQuery = Get2faSettingsSlice(
		currentScreen === "auth-app-setup" && !mfaUser?.data.twoFactor
	);

	const handleLoginSuccess = useHandleLoginSuccess();

	const handleCopyRevoveryCodeToClipboard = () => {
		navigator.clipboard.writeText(recoveryCodes.join("\n"));
		customToast("Recovery codes copied to clipboard!", {
			id: "copy-recovery-code",
			type: "success",
		});
	};

	const handleDownloadCodes = () => {
		const element = document.createElement("a");
		const file = new Blob([recoveryCodes.join("\n")], {
			type: "text/plain",
		});
		element.href = URL.createObjectURL(file);
		element.download = "recovery_codes.txt";
		document.body.appendChild(element);
		element.click();
		document.removeChild(element);
	};

	const {
		handleSubmit: authHandleSubmit,
		setValue: authSetValue,
		watch: authWatch,
		setError: authSetError,
		formState: { errors: authErrors },
		setError,
	} = useForm<OTPFormValues>({
		resolver: zodResolver(
			createOTPSchema({
				length: currentScreen === "recovery-code-login" ? 21 : 6,
				acceptStrings: currentScreen === "recovery-code-login",
			})
		),
	});

	const verify2faAppOTPMutation = Verify2faAppOTPSlice();
	const sendCodeToEmailMutation = SendCodeToEmailSlice();

	const manualVerify2faAppOTPMutation = ManualVerify2faAppOTPSlice();

	const onMAnualRedirect = () => {
		handleLoginSuccess(userData);
	};

	const onManualVerifySubmit: SubmitHandler<OTPFormValues> = async (data) => {
		const payload = {
			code: data.otp,
			token: mfaUser?.data.token ?? "",
		};
		manualVerify2faAppOTPMutation.mutate(payload, {
			onSuccess: (data) => {
				customToast("Two-factor authentication enabled", {
					id: "manual-2fa",
					type: "success",
				});
				setRecoveryCodes(data?.data.recoveryCodes);
				setCurrentScreen("recovery-code");
				setUserData(data);
			},
			onError: (error) => {
				if (error?.response) {
					const errorMessage = error.response.data.message;
					authSetError("otp", {
						message: errorMessage,
					});
				} else {
					authSetError("otp", {
						message: "An error occurred. Please try again later.",
					});
				}
			},
		});
	};

	const onVerifyLoginSubmit: SubmitHandler<OTPFormValues> = async (data) => {
		console.log(rememberAuth);
		const payload: VerifyApp2FAProps = {
			code: data.otp,
			token: mfaUser?.data.token ?? "",
			remember_me: rememberAuth?.rememberMe ?? false,
			remember_token:
				rememberAuth?.rememberToken && rememberAuth?.rememberToken,
		};
		verify2faAppOTPMutation.mutate(payload, {
			onSuccess: (data) => {
				// customToast(data?.message ?? "2FA Validation Successful", {
				// 	id: "login-2fa",
				// 	type: "success",
				// 	duration: 3000,
				// });
				handleLoginSuccess(data);
				resetMfaUser();
			},
			onError: (error) => {
				setAttemptCount(attemptCount + 1);
				if (error?.response?.status === 401) {
					customToast("Auth Timeout", {
						id: "auth-timeout",
						type: "error",
						duration: 5000,
					});
					resetMfaUser();
				}
				if (error?.response) {
					const errorMessage = error.response.data.message;
					authSetError("otp", {
						message: errorMessage,
					});
				} else {
					authSetError("otp", {
						message: "An error occurred. Please try again later.",
					});
				}
			},
		});
	};

	const handleSendCodeToEmail = () => {
		const payload = {
			token: mfaUser?.data.token ?? "",
		};
		sendCodeToEmailMutation.mutate(payload, {
			onSuccess: (data) => {
				customToast(`${data?.message || "OTP sent to email"}`, {
					id: "send-code-to-email",
					type: "success",
				});
			},
			onError: (error) => {
				if (error?.response) {
					const errorMessage = error.response.data.message;
					customToast(`${errorMessage || "OTP not sent to email"}`, {
						id: "send-code-to-email",
						type: "error",
					});
				} else {
					customToast("An error occurred. Please try again later.", {
						id: "send-code-to-email",
						type: "error",
					});
				}
			},
		});
	};

	useEffect(() => {
		if (mfaUser?.data.twoFactor) {
			setCurrentScreen("verify-login");
		}
	}, [mfaUser]);

	const RenderScreen = (activeScreen: string) => {
		switch (activeScreen) {
			case "default":
				return (
					<div
						className={cn(
							"relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]",
							{
								"py-6": !user?.two_factor_skip,
								"pt-6": user?.two_factor_skip,
							}
						)}
					>
						<div className="flex flex-col space-y-2 px-4 md:px-8">
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Multi-factor Authentication
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								For added security, set up secondary security
								verification.
							</p>
						</div>
						<div className="space-y-6 px-4 md:px-8">
							{/* <div className="flex items-center justify-between space-x-1">
								<div className="flex flex-col">
									<h3 className="text-[14px] font-medium text-[#323539]">
										Email Authentication
									</h3>
									<p className="text-[13px] -tracking-[1%] text-[#858C95]">
										You will receive an email with an OTP.{" "}
									</p>
								</div>
								<Button
									// disabled={loginUserMutaion.isPending}
									disabled
									className={
										"h-[46px] w-full font-semibold text-[#858C95] hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#323539] md:h-10 md:w-[63px] mmd:text-[14px]"
									}
									type="submit"
									variant={"outline"}
								>
									{manualVerify2faAppOTPMutation.isPending ? (
										<div className="">
											<Loader size={24} />
										</div>
									) : (
										"Setup"
									)}
								</Button>
							</div> */}
							<div className="flex items-center justify-between">
								<div className="flex flex-col">
									<h3 className="text-[14px] font-medium text-[#323539]">
										Authenticator App
									</h3>
									<p className="text-[13px] -tracking-[1%] text-[#858C95]">
										Link your authenticator app. Scan QR
										code to access OTP.
									</p>
								</div>
								<Button
									disabled={
										manualVerify2faAppOTPMutation.isPending
									}
									className={
										"h-[46px] flex-shrink-0 px-6 font-semibold text-[#858C95] hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#323539] md:h-10 md:w-[63px] mmd:text-[14px]"
									}
									type="button"
									onClick={() =>
										setCurrentScreen("auth-app-setup")
									}
									variant={"outline"}
								>
									Setup
								</Button>
							</div>
						</div>
						{user?.two_factor_skip && (
							<div className="flex items-center justify-end space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
								<LoaderButton
									disabled={
										manualVerify2faAppOTPMutation.isPending ||
										skip2faMutation.isPending
									}
									loading={
										manualVerify2faAppOTPMutation.isPending ||
										skip2faMutation.isPending
									}
									loaderSize={20}
									className={
										"h-[46px] w-full bg-[#195388] text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
									}
									type="submit"
									onClick={() => skip2faMutation.mutate()}
								>
									Skip
								</LoaderButton>
							</div>
						)}
					</div>
				);
			case "auth-app-setup":
				return (
					<form
						onSubmit={authHandleSubmit(onManualVerifySubmit)}
						className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
					>
						<div className="relative flex flex-col space-y-2 px-4 md:px-8 md:pl-[46px]">
							<Button
								disabled={verify2faAppOTPMutation.isPending}
								className={
									"absolute left-1 top-1 h-[46px] font-semibold text-[#858C95] md:h-10 mmd:text-[15p]"
								}
								type="button"
								size={"icon-sm"}
								variant={"ghost"}
								onClick={() => setCurrentScreen("default")}
							>
								<IoIosArrowBack size={18} color="#323539" />
							</Button>
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Authenticator App
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								As you selected a third part app as your
								authentication, please scan the QR code below.
							</p>
						</div>
						<div className="space-y-6 px-4 md:px-8">
							<div className="flex flex-col items-center justify-center space-y-4">
								<h3 className="m-0 p-0 text-[#858C95]">
									Scan QR Code to Access OTP
								</h3>
								<div
									dangerouslySetInnerHTML={{
										__html:
											get2faSettingsQuery?.data?.qrcode ??
											"",
									}}
								/>
							</div>
							<div className="flex items-center justify-center text-center">
								<OTPInput
									label="Enter OTP sent to your Authenticator app"
									labelStyles="text-[14px] font-medium text-[#323539]"
									length={6}
									value={authWatch("otp") ?? ""}
									onChange={(value) => {
										authSetValue("otp", value);
									}}
									error={authErrors.otp?.message}
									onComplete={() => {
										// console.log("Completed:", value);
									}}
								/>
								{/* {authErrors.root?.message && (
									<p
										id="formError"
										role="alert"
										className="mt-1.5 px-4 text-sm text-[#c9312c] md:px-8"
									>
										{authErrors.root?.message}
									</p>
								)} */}
							</div>
						</div>
						<div className="flex items-center justify-between space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
							<Button
								disabled={verify2faAppOTPMutation.isPending}
								className={
									"h-[46px] w-full font-semibold text-primary hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[136px] mmd:text-[15px]"
								}
								type="button"
								variant={"outline-primary"}
								onClick={() =>
									setCurrentScreen("manual-auth-app-setup")
								}
							>
								Add Manually
							</Button>
							{/* <CustomButton
								disabled={verify2faAppOTPMutation.isPending}
								className={
									"h-[46px] w-full bg-[#195388] text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
								}
								type="submit"
							>
								{verify2faAppOTPMutation.isPending ? (
									<div className="">
										<Loader size={24} />
									</div>
								) : (
									"Continue"
								)}
							</CustomButton> */}
							<Button
								disabled={
									manualVerify2faAppOTPMutation.isPending ||
									authWatch("otp")?.length !== 6
								}
								className={
									"h-[46px] w-full font-semibold text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
								}
								type="submit"
								variant={"default"}
							>
								{manualVerify2faAppOTPMutation.isPending ? (
									<div className="">
										<Loader size={24} />
									</div>
								) : (
									"Continue"
								)}
							</Button>
						</div>
						<RequestIsLoading
							isWhite
							isLoading={get2faSettingsQuery.isPending}
						/>
					</form>
				);
			case "email-setup":
				return (
					<div className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]">
						<Button
							disabled={manualVerify2faAppOTPMutation.isPending}
							className={
								"h-[46px] w-full font-semibold text-[#858C95] hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
							}
							type="button"
							size={"icon-sm"}
							variant={"ghost"}
							onClick={() => setCurrentScreen("default")}
						>
							Back
						</Button>
						<div className="flex flex-col space-y-2 px-4 md:px-8 md:pl-[46px]">
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Multi-factor Authentication
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								As you opted for email authentication, please
								check your email to view the OTP we sent you.
							</p>
						</div>
						<div className="space-y-6 px-4 md:px-8 md:pl-[46px]">
							<div className="flex items-center justify-between">
								<OTPInput
									label={`Enter OTP sent to your email : ${partiallyUnmaskEmail(user?.email ?? "", 2)}`}
									labelStyles="text-[14px] font-medium text-[#323539]"
									length={6}
									value={authWatch("otp") ?? ""}
									onChange={(value) => {
										authSetValue("otp", value);
									}}
									error={authErrors.otp?.message}
									onComplete={(value) => {
										console.log("Completed:", value);
									}}
								/>
							</div>
						</div>
						<div className="flex items-center justify-end space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
							<Button
								disabled={
									manualVerify2faAppOTPMutation.isPending
								}
								className={
									"h-[46px] w-full font-semibold text-[#858C95] hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
								}
								type="button"
								variant={"ghost"}
								onClick={() => setCurrentScreen("default")}
							>
								Back
							</Button>
							<LoaderButton
								disabled={
									manualVerify2faAppOTPMutation.isPending
								}
								loading={
									manualVerify2faAppOTPMutation.isPending
								}
								loaderSize={20}
								className={
									"h-[46px] w-full bg-[#195388] text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
								}
								type="submit"
							>
								Continue
							</LoaderButton>
						</div>
					</div>
				);
			case "verify-login":
				return (
					<form
						onSubmit={authHandleSubmit(onVerifyLoginSubmit)}
						className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
					>
						<div className="relative flex flex-col space-y-2 px-4 md:px-8 md:pl-8">
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Two-Factor Authentication (2FA)
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								Please enter the 6-digit verification code sent
								to your email or authenticator app to securely
								access your account.
							</p>
						</div>
						<div className="flex flex-col items-center space-y-6 px-4 md:px-8">
							<div className="flex flex-wrap items-center justify-center text-center">
								<OTPInput
									label="Enter the code below"
									labelStyles="text-[14px] font-medium text-[#323539]"
									length={6}
									value={authWatch("otp") ?? ""}
									onChange={(value) => {
										authSetValue("otp", value);
										setError("otp", { message: "" });
									}}
									error={authErrors.otp?.message}
									onComplete={(value) => {
										console.log("Completed:", value);
									}}
								/>
							</div>
							<Button
								className={
									"flex-2 h-[46px] w-full text-wrap p-0 font-semibold hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
								}
								type="button"
								variant={"link"}
								onClick={handleSendCodeToEmail}
							>
								{sendCodeToEmailMutation.isPending ? (
									<div className="">
										<Loader size={24} />
									</div>
								) : (
									"Send code to email?"
								)}
							</Button>
						</div>
						<div className="flex items-center justify-between space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
							{attemptCount > 1 && (
								<Button
									className={
										"flex-2 h-[46px] w-full text-wrap p-0 font-semibold hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
									}
									type="button"
									variant={"link"}
									onClick={() => {
										setCurrentScreen("recovery-code-login");
										setAttemptCount(0);
										authSetValue("otp", "");
										setError("otp", { message: "" });
									}}
								>
									Use one of your recovery codes to regain
									access
								</Button>
							)}
							<div className="flex w-auto flex-1 justify-end">
								<Button
									disabled={
										verify2faAppOTPMutation.isPending ||
										authWatch("otp")?.length !== 6
									}
									className={
										"h-[46px] w-full font-semibold text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
									}
									type="submit"
									variant={"default"}
									// onClick={handleDownloadCodes}
								>
									{verify2faAppOTPMutation.isPending ? (
										<div className="">
											<Loader size={24} />
										</div>
									) : (
										"Continue"
									)}
								</Button>
							</div>
						</div>
					</form>
				);
			case "recovery-code-login":
				return (
					<form
						onSubmit={authHandleSubmit(onVerifyLoginSubmit)}
						className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
					>
						<div className="relative flex flex-col space-y-2 px-4 md:px-8 md:pl-8">
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Authenticator App
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								As you selected a third part app as your
								authentication, please follow the instructions
								below
							</p>
						</div>
						<div className="space-y-6 px-4 md:px-8">
							<div className="flex flex-wrap items-center justify-start text-center">
								<OTPInput
									label="Please enter one of your recovery codes to regain access to your account."
									labelStyles="text-[14px] font-medium text-[#323539]"
									length={21}
									value={authWatch("otp") ?? ""}
									onChange={(value) => {
										authSetValue("otp", value);
										setError("otp", { message: "" });
									}}
									error={authErrors.otp?.message}
									onComplete={(value) => {
										console.log("Completed:", value);
									}}
									// seperator={true}
									// seperatorLine={10}
									textMode={true}
								/>
							</div>
						</div>
						<div className="flex items-center justify-between space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
							<Button
								className={
									"flex-2 h-[46px] w-full text-wrap font-semibold hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
								}
								type="button"
								variant={"outline"}
								onClick={() => {
									setCurrentScreen("verify-login");
									authSetValue("otp", "");
									setError("otp", { message: "" });
								}}
							>
								Back
							</Button>
							<div className="flex w-auto flex-1 justify-end">
								<Button
									disabled={
										verify2faAppOTPMutation.isPending ||
										authWatch("otp")?.length !== 21
									}
									className={
										"h-[46px] w-full font-semibold text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
									}
									type="submit"
									variant={"default"}
								>
									{verify2faAppOTPMutation.isPending ? (
										<div className="">
											<Loader size={24} />
										</div>
									) : (
										"Continue"
									)}
								</Button>
							</div>
						</div>
						<RequestIsLoading
							isWhite
							isLoading={get2faSettingsQuery.isPending}
						/>
					</form>
				);
			case "manual-auth-app-setup":
				return (
					<form
						onSubmit={authHandleSubmit(onManualVerifySubmit)}
						className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
					>
						<div className="relative flex flex-col space-y-2 px-4 md:px-8 md:pl-[46px]">
							<Button
								disabled={
									manualVerify2faAppOTPMutation.isPending ||
									recoveryCodes.length > 0
								}
								className={
									"absolute left-1 top-1 h-[46px] font-semibold text-[#858C95] md:h-10 mmd:text-[15p]"
								}
								type="button"
								size={"icon-sm"}
								variant={"ghost"}
								onClick={() =>
									setCurrentScreen("auth-app-setup")
								}
							>
								<IoIosArrowBack size={18} color="#323539" />
							</Button>
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Authenticator App
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								As you selected a third party app as your
								authentication, please follow the instructions
								below
							</p>
						</div>
						<div className="space-y-14 px-4 md:px-8 md:pl-[46px]">
							<div className="flex flex-col items-start justify-center">
								<h3 className="m-0 p-0 text-base font-medium text-[#323539]">
									1. Enter this code in your authenticator app
								</h3>
								<div className="flex w-full items-center justify-between space-x-2 pl-2.5">
									<h2 className="text-xl font-semibold tracking-widest">
										{get2faSettingsQuery?.data?.code ?? ""}
									</h2>
									<Button
										disabled={
											manualVerify2faAppOTPMutation.isPending
										}
										className={
											"flex h-[28px] items-center space-x-2 font-semibold text-[#858C95] md:h-[28px] mmd:text-[15p]"
										}
										type="button"
										variant={"outline"}
										onClick={() =>
											setCurrentScreen("default")
										}
									>
										<i className="mgc_copy_2_fill before:!text-[#858C95]" />
										<span className="font-normal leading-none text-[#323539]">
											Copy
										</span>
									</Button>
								</div>
							</div>
							<div className="mb-10 flex flex-col items-start justify-center">
								<h3 className="m-0 p-0 text-base font-medium text-[#323539]">
									2. Enter the code from your app below
								</h3>
								<div className="flex w-full items-center justify-between space-x-2 pl-2.5">
									<OTPInput
										length={6}
										value={authWatch("otp") ?? ""}
										onChange={(value) => {
											authSetValue("otp", value);
										}}
										error={authErrors.otp?.message}
										onComplete={(value) => {
											console.log("Completed:", value);
										}}
									/>
								</div>
							</div>
						</div>
						<div className="flex items-center justify-between space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
							<Button
								disabled={
									manualVerify2faAppOTPMutation.isPending ||
									recoveryCodes.length > 0
								}
								className={
									"h-[46px] w-full font-semibold text-primary hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[136px] mmd:text-[15px]"
								}
								type="button"
								variant={"outline-primary"}
								onClick={() =>
									setCurrentScreen("auth-app-setup")
								}
							>
								Scan QR code
							</Button>
							<LoaderButton
								disabled={
									manualVerify2faAppOTPMutation.isPending
								}
								loading={
									manualVerify2faAppOTPMutation.isPending
								}
								loaderSize={20}
								className={
									"h-[46px] w-full bg-[#195388] text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
								}
								type="submit"
							>
								Continue
							</LoaderButton>
						</div>
					</form>
				);
			case "recovery-code":
				return (
					<div className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]">
						<div className="flex flex-col space-y-2 px-4 md:px-8">
							<h3 className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
								Recovery Code
							</h3>
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								These recovery codes are essential for accessing
								your account if you lose access to your 2FA
								device. Please store them in a safe place.
							</p>
						</div>
						<div className="space-y-6 px-4 md:px-8">
							<div className="flex items-start justify-center space-x-2">
								<div className="flex flex-col">
									{recoveryCodes.map((code, index) => (
										<h3
											key={index}
											className="text-xl font-medium text-[#323539]"
										>
											{code}
										</h3>
									))}
								</div>
								<Button
									disabled={
										manualVerify2faAppOTPMutation.isPending
									}
									className={
										"flex h-[28px] items-center space-x-2 font-semibold text-[#858C95] md:h-[28px] mmd:text-[15p]"
									}
									type="button"
									variant={"outline"}
									onClick={handleCopyRevoveryCodeToClipboard}
								>
									<i className="mgc_copy_2_fill before:!text-[#858C95]" />
									<span className="font-normal leading-none text-[#323539]">
										Copy
									</span>
								</Button>
							</div>
						</div>
						<div className="flex flex-col space-y-2 px-4 md:px-8">
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								<strong>Important:</strong> Each code can only
								be used once. If you run out of codes, you will
								need to generate new ones from your account
								settings.
							</p>
						</div>
						<div className="flex flex-col space-y-2 px-4 md:px-8">
							<p className="font-normal tracking-[-1%] text-[#858C95]">
								<strong>Note:</strong> Do not share these codes
								with anyone. They provide direct access to your
								account.
							</p>
						</div>
						<div className="flex items-center justify-between space-x-2 rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8 mmd:flex-col mmd:space-y-6">
							<Button
								disabled={
									manualVerify2faAppOTPMutation.isPending
								}
								className={
									"h-[46px] w-full font-semibold text-[#858C95] hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-max mmd:text-[15px]"
								}
								type="button"
								variant={"outline-primary"}
								onClick={handleDownloadCodes}
							>
								Download Codes
							</Button>
							<LoaderButton
								disabled={
									manualVerify2faAppOTPMutation.isPending
								}
								loading={
									manualVerify2faAppOTPMutation.isPending
								}
								loaderSize={20}
								className={
									"h-[46px] w-full bg-[#195388] text-white hover:border-[#72F4E8] hover:bg-[#72F4E8] hover:text-[#053969] md:h-10 md:w-[104px] mmd:text-[15px]"
								}
								type="button"
								onClick={onMAnualRedirect}
							>
								Continue
							</LoaderButton>
						</div>
					</div>
				);
			default:
				return "a";
		}
	};

	// useEffect(() => {
	// 	reset();
	// }, []);

	return RenderScreen(currentScreen);
};

export default MFACard;
