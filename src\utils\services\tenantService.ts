import { AuthMethod, TenantConfig } from "../../types/tenant";

export const tenantConfigs: Record<string, TenantConfig> = {
	default: {
		id: "default",
		name: "Migranium",
		subdomain: "",
		logo: "/logo.png",
		authMethods: [
			{ type: "password", icon: "🔒" },
			{ type: "google", icon: "🔍" },
			{ type: "sso", icon: "🔑" },
		],
		theme: {
			primaryColor: "#0066cc",
			secondaryColor: "#003366",
			backgroundColor: "#ffffff",
		},
		loginType: "standard",
		showPasswordLogin: true,
	},
};

export async function fetchTenantConfig(
	subdomain: string
): Promise<TenantConfig> {
	return tenantConfigs[subdomain] || tenantConfigs.default;
}

export interface SSOVerificationResponse {
	data: {
		id: number;
		name: string;
		role: string;
		products: string[];
		logo_url: string | null;
		domain: string;
		domain_changed: number;
		sso_enable: boolean;
		sso_login_url: string;
	};
	status: boolean;
	message: string;
}

/**
 * Merges SSO verification data with a tenant config
 * @param tenantConfig - The existing tenant configuration
 * @param ssoData - The SSO verification response data
 * @returns An updated tenant configuration
 */
export const mergeTenantWithSSOData = (
	tenantConfig: TenantConfig,
	ssoData: SSOVerificationResponse
): TenantConfig => {
	if (!ssoData?.data) {
		return tenantConfig;
	}

	return {
		...tenantConfig,
		id: String(ssoData.data.id), // Convert number to string
		name: ssoData.data.name,
		subdomain: ssoData.data.domain,
		// If logo_url exists, use it; otherwise keep the existing logo
		logo: ssoData.data.logo_url || tenantConfig.logo,
		// Update auth methods based on SSO availability
		authMethods: updateAuthMethods(
			tenantConfig.authMethods || [],
			ssoData.data.sso_enable
		),
		// Set login type based on SSO or other factors
		loginType: determineLoginType(ssoData.data, tenantConfig),
		// Update SSO-specific settings
		showPasswordLogin: !ssoData.data.sso_enable, // Disable password login if SSO is enabled
		// Set SSO button text if not already set
		ssoButtonText: ssoData.data.sso_enable
			? tenantConfig.ssoButtonText || "Sign in with SSO"
			: tenantConfig.ssoButtonText,
		// Keep other properties that exist on tenantConfig but not in ssoData
	};
};

/**
 * Updates auth methods based on SSO status
 * @param currentMethods - Current auth methods
 * @param ssoEnabled - Whether SSO is enabled
 * @returns Updated auth methods array
 */
const updateAuthMethods = (
	currentMethods: AuthMethod[],
	ssoEnabled: boolean
): AuthMethod[] => {
	// Create a copy of the current methods
	const updatedMethods = [...currentMethods];

	// If SSO is enabled, add SSO as an auth method if it doesn't exist
	if (ssoEnabled) {
		// Check if "sso" exists as AuthMethod (this assumes AuthMethod is an enum or type with "sso" as a value)
		// We need to make sure we're checking for the correctly typed value, not just a string
		const ssoAuthMethod = "sso" as unknown as AuthMethod;
		if (!updatedMethods.includes(ssoAuthMethod)) {
			updatedMethods.push(ssoAuthMethod);
		}
	} else {
		// If SSO is disabled, remove it from auth methods
		const ssoAuthMethod = "sso" as unknown as AuthMethod;
		return updatedMethods.filter((method) => method !== ssoAuthMethod);
	}

	return updatedMethods;
};

/**
 * Determines the login type based on SSO data
 * @param ssoData - SSO verification data
 * @param tenantConfig - Current tenant config
 * @returns The login type (standard or custom)
 */
const determineLoginType = (
	ssoData: SSOVerificationResponse["data"],
	tenantConfig: TenantConfig
): "standard" | "custom" => {
	// If SSO is enabled and there's an SSO login URL, use custom login
	if (ssoData.sso_enable && ssoData.sso_login_url) {
		return "custom";
	}

	// Otherwise, use the existing login type or default to standard
	return tenantConfig.loginType || "standard";
};

/**
 * Get the domain from the current window location (client-side only)
 * @returns The domain extracted from window.location.hostname
 */
export const getCurrentDomain = (): string => {
	if (typeof window === "undefined") {
		return "";
	}

	return window.location.hostname;
};

/**
 * Get the subdomain from the current window location (client-side only)
 * @returns The subdomain extracted from window.location.hostname
 */
export const getCurrentSubdomain = (): string => {
	if (typeof window === "undefined") {
		return "";
	}

	const hostname = window.location.hostname;
	// Split by dots and take the first part
	const parts = hostname.split(".");

	// If we have a subdomain (e.g., "subdomain.domain.com")
	if (parts.length > 2) {
		return parts[0];
	}

	// If no subdomain (e.g., "domain.com")
	return "";
};
