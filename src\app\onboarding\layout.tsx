"use client";

import CloseOnboardingProcess from "@/components/Onboarding/CloseOnboardingProcess";
import RequestIsLoading from "@/components/RequestIsLoading";
import RequireAuth from "@/hooks/useRequireAuth";
import { useGetBusinessCategories } from "@/store/slices/onboarding";
import { usePathname } from "next/navigation";
import React, { ReactNode, useState } from "react";
import { IoClose } from "react-icons/io5";
import { RxChevronLeft } from "react-icons/rx";
import useUserStore from "../../store/useUserStore";

const OnboardingLayout: React.FC<{
	children: ReactNode;
}> = ({ children }) => {
	const pathname = usePathname();

	const { onboardingState, user, setOnboardingState } = useUserStore((s) => ({
		onboardingState: s.onboardingState,
		user: s.user,
		setOnboardingState: s.setOnboardingState,
	}));

	const getBusinessCategoriesQuery = useGetBusinessCategories();

	const [showCloseOnboardingProcess, setShowCloseOnboardingProcess] =
		useState(false);

	React.useEffect(() => {
		setOnboardingState(1);
	}, [user]);

	return (
		<>
			<RequireAuth />
			<main className="relative flex min-h-screen flex-col">
				<nav className="flex w-full justify-center bg-white">
					<div className="mx-4 flex w-full max-w-[1244px] items-center justify-between">
						<RxChevronLeft
							size={24}
							fill="#323539"
							className={`${
								pathname.includes("/onboarding/about-business")
									? "invisible"
									: "cursor-pointer"
							}`}
							onClick={() => {
								setOnboardingState(
									onboardingState - 1 !== 0
										? onboardingState - 1
										: 1
								);
							}}
						/>

						<div className="flex max-w-[656px] flex-1 justify-between pb-3 pt-2">
							<OnboardingItem
								isCompleted={
									onboardingState === 1
										? "in-progress"
										: onboardingState > 1
											? "yes"
											: "no"
								}
								title={"Add Organisation"}
							/>
							<OnboardingItem
								isCompleted={
									onboardingState === 2
										? "in-progress"
										: onboardingState > 2
											? "yes"
											: "no"
								}
								title={"Add Operating Hours"}
							/>
							{/* <OnboardingItem
								isCompleted={
									onboardingState === 3 ? "in-progress" : "no"
								}
								title={"Add Payment"}
							/> */}
						</div>

						<IoClose
							size={24}
							fill="#323539"
							className="cursor-pointer"
							onClick={() => setShowCloseOnboardingProcess(true)}
						/>
					</div>
				</nav>
				<div className="clock-background-image flex h-full w-full flex-1 justify-center self-stretch px-4 pb-4 pt-[16px]">
					{children}
				</div>
			</main>
			<RequestIsLoading
				isWhite
				isLoading={getBusinessCategoriesQuery.isLoading}
				size={20}
			/>
			<CloseOnboardingProcess
				show={showCloseOnboardingProcess}
				setShow={setShowCloseOnboardingProcess}
			/>
		</>
	);
};

const OnboardingItem: React.FC<{
	isCompleted: "yes" | "no" | "in-progress";
	title: string;
}> = ({ isCompleted, title }) => {
	return (
		<li
			className={`flex flex-1 items-center space-x-2 py-2.5 leading-[22px] ${
				isCompleted === "in-progress" || isCompleted === "yes"
					? "border-b-2 border-b-[#053969] font-semibold text-[#195388]"
					: "border-b-2 border-b-[#E5E5E7] font-medium text-[#323539]"
			}`}
		>
			{/* <PiCheckCircleLight
				fill="#195388"
				className={`${isCompleted !== "in-progress" && "hidden"}`}
			/> */}
			{isCompleted === "in-progress" && (
				<i className="mgc_check_circle_line before:!text-[#195388]" />
			)}

			{/* <PiCheckCircleFill
				fill="#195388"
				className={`${isCompleted !== "yes" && "hidden"}`}
			/> */}
			{isCompleted === "yes" && (
				<i className="mgc_check_circle_fill before:!text-[#195388]" />
			)}
			<p>{title}</p>
		</li>
	);
};

export default OnboardingLayout;
