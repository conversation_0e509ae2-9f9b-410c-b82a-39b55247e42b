import { z } from "zod";
import { AuthLoginResponse } from "./sign-in";

export const createOTPSchema = z.object({
	otp: z
		.string()
		.min(6, "OTP must be 6 digits")
		.max(6, "OTP must be 6 digits")
		.regex(/^[0-9]+$/, "OTP must contain only numbers"),
});

export interface GetMFASettingsResponse {
	status: boolean;
	message: string;
	qrcode: string;
	code: string;
}

export interface VerifyApp2FAErrorReponse {
	status: false;
	message: "Invalid token";
	error: object;
}

export interface VerifyApp2FAProps {
	code: string;
	token: string;
	remember_me?: boolean;
	remember_token?: string;
}

export interface SendCodeToEmailProps {
	token: string;
}
export interface ManualVerifyApp2FAProps {
	code: string;
}

export interface ManualVerifyApp2FAResponse {
	status: boolean;
	message: string;
	data: AuthLoginResponse["data"] & { recoveryCodes: string[] };
}

export interface ManualVerifyApp2FAErrorResponse {
	status: false;
	message: "Invalid token";
	error: object;
}

export type OTPFormValues = z.infer<typeof createOTPSchema>;
