import { z } from "zod";

export type ObjectValues<T> = T[keyof T];

export const AcceptTeamMemberSchema = z
	.object({
		password: z
			.string()
			.min(8, { message: "Password must be at least 8 characters long" })
			.regex(/[A-Z]/, {
				message: "Password must contain at least one uppercase letter",
			})
			.regex(/[a-z]/, {
				message: "Password must contain at least one lowercase letter",
			})
			.regex(/\d/, {
				message: "Password must contain at least one number",
			})
			.regex(/[@$!%*?&#]/, {
				message: "Password must contain at least one special character",
			}),
		password_confirmation: z.string(),
		token: z.string().min(1, { message: "Token too short" }),
		agree_to_terms: z.boolean(),
	})
	.refine((data) => data.agree_to_terms === true, {
		message: "You must agree to the terms and conditions",
		path: ["agree_to_terms"],
	});

export type AcceptTeamMemberType = z.infer<typeof AcceptTeamMemberSchema>;

export interface AcceptTeamMemberResponse {
	status: boolean;
	message: string;
	data: AcceptTeamMemberResponseData;
}

export interface AcceptTeamMemberResponseData {
	id: number;
	name: string;
	email: string;
	email_new: string | null;
	two_factor_secret: string | null;
	two_factor_recovery_codes: string | null;
	two_factor_confirmed_at: string | null;
	two_factor_method: string | null;
	google_user_id: string | null;
	role: "TEAM_MEMBER";
	job_title: string | null;
	stripe_customer_id: string | null;
	created_at: string | Date;
	updated_at: string | Date;
	stripe_id: string | null;
	pm_type: string | null;
	pm_last_four: string | null;
	trial_ends_at: string | null;
	business_id: number;
	is_email_verified: boolean;
	is_active: number;
	company_id: string | null;
	phone_number: string;
}

export interface AcceptTeamMemberErrorResponse {
	status: boolean;
	message: string;
	error: string;
}

export interface AcceptTeamMemberErrorNoParamsResponse {
	status: boolean;
	message: string;
	errors: Record<string, string[]>;
}

export const TeamMemberConst = {
	BusinessManager: "BUSINESS_MANAGER",
	LocationManager: "LOCATION_MANAGER",
	StationManager: "STATION_MANAGER",
	ServiceProvider: "SERVICE_PROVIDER",
	TeamMember: "TEAM_MEMBER",
} as const;

export type TeamMemberEnumType = ObjectValues<typeof TeamMemberConst>;
