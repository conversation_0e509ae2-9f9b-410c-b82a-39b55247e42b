# bitbucket-pipelines.yml for LandingPage repo

# Use Node.js 20 LTS for Web Crypto & modern features
image: node:20

pipelines:
    branches:
        # Dev branch: build & deploy to migranium-dev (ambitious-sky)…
        dev:
            - step:
                  name: Build & Deploy (LandingPage Dev)
                  size: 2x
                  caches:
                      - node
                  script:
                      # 1) Increase Node heap to avoid OOM
                      - export NODE_OPTIONS="--max_old_space_size=4096"

                      # 2) Download and load environment vars
                      - echo "Fetching Dev .env..."
                      - curl -o .env "$DEV_AZURE_BLOB_SAS_URL"
                      - export $(grep -v '^#' .env | xargs)

                      # 3) Install deps & build
                      - echo "Installing dependencies..."
                      - npm install --legacy-peer-deps
                      - echo "Building app..."
                      - npm run build

                      # 4) Install Static Web Apps CLI
                      - echo "Installing SWA CLI..."
                      - npm install -g @azure/static-web-apps-cli

                      # 5) Deploy to Azure Static Web App (Dev)
                      - echo "Deploying to migranium-dev..."
                      - swa deploy dist \
                        --app-name migranium-dev \
                        --deployment-token $LANDINGPAGE_DEV_SWA_TOKEN \
                        --subscription $AZURE_SUBSCRIPTION_ID

        # Staging branch: build & deploy to migranium-staging (kind-ocean…)
        staging:
            - step:
                  name: Build & Deploy (LandingPage Staging)
                  size: 2x
                  caches:
                      - node
                  script:
                      - export NODE_OPTIONS="--max_old_space_size=4096"
                      - echo "Fetching Staging .env..."
                      - curl -o .env "$STAGING_AZURE_BLOB_SAS_URL"
                      - export $(grep -v '^#' .env | xargs)

                      - echo "Installing dependencies..."
                      - npm install --legacy-peer-deps
                      - echo "Building app..."
                      - npm run build

                      - echo "Installing SWA CLI..."
                      - npm install -g @azure/static-web-apps-cli

                      - echo "Deploying to migranium-staging..."
                      - swa deploy dist \
                        --app-name migranium-staging \
                        --deployment-token $LANDINGPAGE_STAGING_SWA_TOKEN \
                        --subscription $AZURE_SUBSCRIPTION_ID

        # Main branch: build & deploy to LandingPage (prod, nice-pebble…)
        main:
            - step:
                  name: Build & Deploy (LandingPage Prod)
                  size: 2x
                  caches:
                      - node
                  script:
                      - export NODE_OPTIONS="--max_old_space_size=4096"
                      - echo "Fetching Prod .env..."
                      - curl -o .env "$AZURE_BLOB_SAS_URL"
                      - export $(grep -v '^#' .env | xargs)

                      - echo "Installing dependencies..."
                      - npm install --legacy-peer-deps
                      - echo "Building app..."
                      - npm run build

                      - echo "Installing SWA CLI..."
                      - npm install -g @azure/static-web-apps-cli

                      - echo "Deploying to LandingPage..."
                      - swa deploy dist \
                        --app-name LandingPage \
                        --deployment-token $LANDINGPAGE_SWA_TOKEN \
                        --subscription $AZURE_SUBSCRIPTION_ID

    pull-requests:
        # Build & preview PRs targeting dev
        dev:
            - step:
                  name: PR Build & Preview (LandingPage Dev)
                  size: 2x
                  caches:
                      - node
                  script:
                      - export NODE_OPTIONS="--max_old_space_size=4096"
                      - echo "Fetching PR .env..."
                      - curl -o .env "$DEV_AZURE_BLOB_SAS_URL"
                      - export $(grep -v '^#' .env | xargs)

                      - echo "Installing dependencies..."
                      - npm install --legacy-peer-deps
                      - echo "Building app..."
                      - npm run build

                      - echo "Installing SWA CLI..."
                      - npm install -g @azure/static-web-apps-cli

                      - echo "Preview deploying to migranium-dev..."
                      - swa deploy dist \
                        --app-name migranium-dev \
                        --deployment-token $LANDINGPAGE_DEV_SWA_TOKEN \
                        --subscription $AZURE_SUBSCRIPTION_ID \
                        --validate-only
