"use client";

import {
	ResetParams,
	ResetPasswordDataErrorResponse,
	ResetSchema,
} from "@/types/forgot-reset";
import { zodResolver } from "@hookform/resolvers/zod";
import { useRouter, useSearchParams } from "next/navigation";
import React, { Suspense } from "react";
import { SubmitHandler, useForm } from "react-hook-form";
import useCustomToast from "../CustomToast";
import { LoaderButton } from "../ui-extended/loader-button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { handleApiErrors } from "@/utils/general";
import { useResetPassword } from "@/store/slices/forgetResetPassword";

const ResetPasswordCard: React.FC = () => {
	const searchParams = useSearchParams();
	const token = searchParams.get("token");
	const router = useRouter();
	const customToast = useCustomToast();

	const {
		register,
		handleSubmit,
		setError,
		formState: { errors },
	} = useForm<ResetSchema>({
		resolver: zodResolver(ResetParams),
		defaultValues: {
			password: "",
			password_confirmation: "",
			token: token as string,
		},
	});

	const resetPasswordMutation = useResetPassword();

	const onSubmit: SubmitHandler<ResetSchema> = async (data) => {
		resetPasswordMutation.mutate(data, {
			onSuccess: () => {
				customToast("Password changed successfully!", {
					id: "reset-password",
					type: "success",
				});
				setTimeout(() => {
					router.replace("/sign-in");
				}, 1000);
			},
			onError: (error) => {
				if (error.response?.data) {
					handleApiErrors(error.response?.data.message, setError);
					customToast("Password reset failed", {
						id: "reset-password",
						type: "error",
					});
				}
			},
		});
	};

	React.useEffect(() => {
		if (!token) {
			customToast("Invalid token", {
				id: "reset-password",
				type: "error",
			});
			setTimeout(() => {
				router.replace("/sign-in");
			}, 1000);
		}
	}, [token]);

	return (
		<>
			<form
				className="z-10 flex w-full max-w-[488px] flex-col space-y-6 rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]"
				onSubmit={handleSubmit(onSubmit)}
			>
				<div className="flex flex-col space-y-2 px-4 md:px-8">
					<h3
						className="text-[22px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]"
						onClick={() =>
							customToast("Invalid token", {
								id: "reset-password",
								type: "error",
							})
						}
					>
						Reset it in just a few steps.
					</h3>
					<p className="font-normal tracking-[-1%] text-[#858C95]">
						Enter your new password.
					</p>
				</div>
				<div className="flex flex-col space-y-6 px-4 md:px-8">
					<div className="flex flex-col space-y-1.5">
						<Label htmlFor="password" className="text-[#323539]">
							Passowrd <span className="text-[#c9312c]">*</span>
						</Label>
						<Input
							id="password"
							type="password"
							max={40}
							{...register("password")}
							aria-describedby={
								errors.password ? "passwordError" : undefined
							}
						/>
						{errors.password?.message && (
							<small
								id="passwordError"
								role="alert"
								className="mt-1.5 text-sm text-[#c9312c]"
							>
								{errors.password?.message}
							</small>
						)}
					</div>
					<div className="flex flex-col space-y-1.5">
						<Label
							htmlFor="confirmPassword"
							className="text-[#323539]"
						>
							Confirm Passowrd{" "}
							<span className="text-[#c9312c]">*</span>
						</Label>
						<Input
							id="password_confirmation"
							type="password"
							max={40}
							{...register("password_confirmation")}
							aria-describedby={
								errors.password_confirmation
									? "password_confirmationError"
									: undefined
							}
						/>
						{errors.password_confirmation?.message && (
							<small
								id="password_confirmationError"
								role="alert"
								className="mt-1.5 text-sm text-[#c9312c]"
							>
								{errors.password_confirmation?.message}
							</small>
						)}
					</div>
				</div>
				<div
					className="rounded-b-[10px] bg-[#FAFBFC] px-4 pb-6 pt-4 md:px-8"
					onClick={() => console.log(errors)}
				>
					<LoaderButton
						disabled={resetPasswordMutation.isPending}
						loading={resetPasswordMutation.isPending}
						loaderSize={20}
						className="h-10 w-fit text-white mmd:text-[15px]"
						type="button"
						onClick={handleSubmit(onSubmit)}
					>
						Submit
					</LoaderButton>
				</div>
			</form>
		</>
	);
};

const ResetPasswordCardWrapper: React.FC = () => {
	return (
		<Suspense fallback={<div />}>
			<ResetPasswordCard />
		</Suspense>
	);
};

export default ResetPasswordCardWrapper;
