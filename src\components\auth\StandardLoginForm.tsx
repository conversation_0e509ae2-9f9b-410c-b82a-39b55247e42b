"use client";
import { TenantConfig } from "@/types/tenant";
import SignInCard from "../Home/Signin/SignInCard";

interface StandardLoginFormProps {
	onSwitchToSSO: () => void;
	tenantConfig?: TenantConfig;
}

export default function StandardLoginForm({
	onSwitchToSSO,
}: StandardLoginFormProps) {
	return (
		<div className="flex w-screen max-w-[1216px] items-center gap-12 pt-16 mmd:flex-col-reverse mmd:pb-12 mmd:pt-20">
			<SignInCard onSwitchToSSO={onSwitchToSSO} />
			<div className="z-10 flex flex-col space-y-3 md:space-y-6">
				<h1 className="text-2xl font-bold text-[#323539] md:text-[40px] md:font-semibold md:leading-[32px] md:tracking-[-1.5%]">
					Welcome Back to Migranium
				</h1>
				<p className="tracking-[-1%] text-[#858C95] sm:text-lg">
					Sign in to continue optimizing your operational efficiency{" "}
					<br className="msm:hidden" /> and enhancing customer
					experiences.
				</p>
			</div>
		</div>
	);
}
