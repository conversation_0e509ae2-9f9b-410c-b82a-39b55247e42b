"use client";

import AboutBusiness from "@/components/Onboarding/AboutBusiness";
import AddOperatingHours from "@/components/Onboarding/AddOperatingHours";
import { useLoginTempUser } from "@/store/slices/auth";
import { onboardingFormSchema, OnboardingFormType } from "@/types/onboarding";
import { zodResolver } from "@hookform/resolvers/zod";
import { NextPage } from "next";
import { useRouter, useSearchParams } from "next/navigation";
import React, { Suspense } from "react";
import { FormProvider, useForm } from "react-hook-form";

const OnboardingContent: NextPage = () => {
	const [countryCode, setCountryCode] = React.useState("+1");
	const router = useRouter();
	const searchParams = useSearchParams();
	const token = searchParams.get("token");
	const loginTempUserMutation = useLoginTempUser();

	const formMethods = useForm<OnboardingFormType>({
		resolver: zodResolver(onboardingFormSchema),
		defaultValues: {
			queue_settings: {
				estimated_wait_time: 15,
				time_zone: Intl.DateTimeFormat().resolvedOptions().timeZone,
				schedule_block_in_minutes: 15,
			},
			business_details: {
				products: ["primary", "room_booking"],
				logo: "",
			},
		},
	});
	React.useLayoutEffect(() => {
		// if (!token) return router.replace("/sign-in");
		if (token)
			loginTempUserMutation.mutate(
				{ token },
				{
					onError: () => {
						return router.replace("/sign-in");
					},
				}
			);
	}, []);

	return (
		<Suspense>
			<FormProvider {...formMethods}>
				<form
					encType="multipart/form-data"
					className="flex w-full justify-center"
				>
					<AboutBusiness
						countryCode={countryCode}
						setCountryCode={setCountryCode}
					/>
					<AddOperatingHours countryCode={countryCode} />
				</form>
				{/* <AddPaymentMethod /> */}
			</FormProvider>
		</Suspense>
	);
};

const Page: NextPage = () => {
	return (
		<Suspense fallback={null}>
			<OnboardingContent />
		</Suspense>
	);
};

export default Page;
