"use client";

import { cn } from "@/lib/utils";
import "@/styles/hamburger.module.css";
import Link from "next/link";
import React, { useEffect, useState, Dispatch, SetStateAction } from "react";
import { Button } from "../ui/button";
import { Menu, X } from "lucide-react";

const NAV_ITEMS = [
	{ label: "Features", href: "/#features" },
	{ label: "Integrations", href: "/#integrations" },
	{ label: "Why Migranium", href: "/#why-migranium" },
	{ label: "Impact", href: "/#impact" },
	{ label: "Contact Us", href: "/#", isContactUs: true },
];

const Navbar: React.FC<{
	isWhite?: boolean;
	showBookADemo: boolean;
	setShowBookDemo: Dispatch<SetStateAction<boolean>>;
	showContactUsModal: boolean;
	setshowContactUsModal: Dispatch<SetStateAction<boolean>>;
	showSignOut?: boolean;
}> = ({ isWhite, setShowBookDemo, setshowContactUsModal }) => {
	const [scrolled, setScrolled] = useState(false);
	const [mobileOpen, setMobileOpen] = useState(false);

	useEffect(() => {
		window.addEventListener("scroll", () => {
			if (window.scrollY > 20) {
				setScrolled(true);
			} else {
				setScrolled(false);
			}
		});
		return () =>
			window.removeEventListener("scroll", () => {
				setScrolled(false);
			});
	}, []);

	return (
		<nav
			className={cn(
				"fixed left-0 top-0 z-50 w-full font-sans transition-all duration-300",
				scrolled || isWhite
					? "left-0 top-4 px-4 sm:h-[68px] sm:px-12"
					: "bg-[#032F57]"
			)}
		>
			<div
				className={cn(
					"mx-auto flex max-w-[1232px] items-center justify-between",
					scrolled || isWhite
						? "h-[56px] rounded-xl border border-gray-200 bg-white px-2 shadow-navbar"
						: "h-[64px] px-4 sm:h-[68px] sm:px-12 xl:h-[72px] xl:px-6"
				)}
			>
				<Link href="/#hero">
					<img
						src={
							scrolled || isWhite
								? "/assets/images/brand/logo-blue.svg"
								: "/assets/images/brand/logo-white.svg"
						}
						alt="Migranium logo"
						className="hidden h-8 w-auto lg:block"
					/>
					<img
						src={
							scrolled || isWhite
								? "/assets/images/logo-blue.svg"
								: "/assets/images/logo.svg"
						}
						alt="Migranium logo"
						className="h-[26.46px] w-auto lg:hidden"
					/>
				</Link>
				<ul className="hidden items-center space-x-1 xl:flex">
					{NAV_ITEMS.map((item) => (
						<li key={item.label}>
							{item.isContactUs ? (
								<button
									onClick={() => setshowContactUsModal(true)}
									className={cn(
										"rounded-lg border-b-2 border-transparent px-4 py-2.5 text-sm font-medium transition-colors",
										scrolled || isWhite
											? "text-[#043B6D] hover:text-[#20CABA]"
											: "text-white hover:bg-[#043B6DA6] hover:text-[#72F4E8]"
									)}
								>
									{item.label}
								</button>
							) : (
								<Link
									href={item.href}
									className={cn(
										"rounded-lg border-b-2 border-transparent px-4 py-2.5 text-sm font-medium transition-colors",
										scrolled || isWhite
											? "text-[#043B6D] hover:text-[#20CABA]"
											: "text-white hover:bg-[#043B6DA6] hover:text-[#72F4E8]"
									)}
								>
									{item.label}
								</Link>
							)}
						</li>
					))}
				</ul>
				<div className="flex items-center gap-8">
					<div className="flex items-center space-x-3">
						<Link href="/sign-in" className="hidden sm:block">
							<Button
								variant={
									scrolled || isWhite
										? "outline"
										: "secondary"
								}
								className={cn(
									"h-9 border px-5 text-xs font-medium",
									scrolled || isWhite
										? "border-transparent bg-white text-[#303741] hover:border-[#303741] hover:border-[#3EC9BC] hover:text-[#3EC9BC]"
										: "border-none bg-transparent text-white"
								)}
							>
								Log In
							</Button>
						</Link>
						<Button
							aria-label="Book a Demo"
							onClick={() => setShowBookDemo(true)}
							disabled={false}
							className={cn(
								"hidden h-9 px-5 text-xs font-medium lg:block",
								scrolled || isWhite
									? "bg-[#043B6D] text-white hover:bg-[#3EC9BC]"
									: "bg-white text-[#032F57] hover:bg-[#72F4E8] hover:text-[#032F57]"
							)}
						>
							Get Started
						</Button>
					</div>
					<button
						className={cn(
							"flex h-8 w-8 flex-col items-center justify-center focus:outline-none xl:hidden",
							scrolled || isWhite
								? "text-[#043B6D]"
								: "text-white"
						)}
						onClick={() => setMobileOpen((v) => !v)}
						aria-label="Toggle navigation menu"
					>
						{mobileOpen ? <X size={20} /> : <Menu size={20} />}
					</button>
				</div>
			</div>
			{mobileOpen && (
				<div
					className={cn(
						"animate-fade-in-down flex w-full flex-col space-y-2 bg-[#032F57] px-4 pb-4 pt-2 xl:hidden"
					)}
				>
					{NAV_ITEMS.map((item) =>
						item.isContactUs ? (
							<button
								key={item.label}
								onClick={() => {
									setshowContactUsModal(true);
									setMobileOpen(false);
								}}
								className="rounded px-2 py-2 text-left text-sm text-white transition-colors hover:bg-[#072F4E] hover:text-[#72F4E8]"
							>
								{item.label}
							</button>
						) : (
							<Link
								key={item.label}
								href={item.href}
								className="rounded px-2 py-2 text-sm text-white transition-colors hover:bg-[#072F4E] hover:text-[#72F4E8]"
								onClick={() => setMobileOpen(false)}
							>
								{item.label}
							</Link>
						)
					)}
					<Link
						href="/sign-in"
						onClick={() => setMobileOpen(false)}
						className="block text-sm sm:hidden"
					>
						<Button className="mt-2 w-full border border-white bg-transparent text-white hover:bg-[#072F4E]">
							Log In
						</Button>
					</Link>
					<Button
						aria-label="Book a Demo"
						onClick={() => setShowBookDemo(true)}
						disabled={false}
						className="mt-2 block w-full bg-white text-sm text-[#043B6D] hover:bg-[#72F4E8] hover:text-[#043B6D] lg:hidden"
					>
						Get Started
					</Button>
				</div>
			)}
		</nav>
	);
};

export default Navbar;

export const scrollWithOffset = (el: HTMLElement | null) => {
	if (!el) return;

	const yCoordinate = el.getBoundingClientRect().top + window.scrollY;
	const yOffset =
		-window.innerHeight / 2 + el.getBoundingClientRect().height / 2;
	window.scrollTo({ top: yCoordinate + yOffset, behavior: "smooth" });
};
