import React, { Dispatch, SetStateAction } from "react";
import { But<PERSON> } from "../ui/button";
import Link from "next/link";

const HeroSection: React.FC<{
	setShowBookDemo: Dispatch<SetStateAction<boolean>>;
}> = ({ setShowBookDemo }) => {
	return (
		<section id="hero">
			<div className="relative mb-[52px] font-inter sm:mb-[66px] md:mb-[82px]">
				<div className="h-[500px] w-full bg-[url('/assets/images/hero-bg.svg')] bg-cover bg-bottom bg-no-repeat md:h-[550px] lg:h-[810px] xl:h-[779px]">
					<div className="relative z-10 mx-auto flex w-full max-w-[288px] flex-col items-center justify-center pt-32 text-center sm:max-w-[312px] md:max-w-[552px] lg:max-w-[900px]">
						<h1 className="text-2xl font-bold leading-tight text-white lg:text-[38px]">
							AI-Powered Solution for{" "}
							<span className="text-[#72F4E8]">Healthcare</span>{" "}
							Operations
						</h1>
						<p className="mx-auto mt-4 max-w-[288px] text-base text-white/70 sm:max-w-4xl sm:text-sm md:text-lg">
							The all-in-one platform that integrates seamlessly
							with EMRs and existing tools to streamline
							workflows, reduce admin tasks, boost efficiency, and
							enhance patient care.
						</p>
						<div className="mt-4 hidden flex-row items-center justify-center gap-4 lg:flex xl:mt-[33px]">
							<Button
								aria-label="Book a Demo"
								onClick={() => setShowBookDemo(true)}
								disabled={false}
								className="rounded-md bg-white px-4 py-2.5 text-sm font-medium text-[#032F57] shadow-none transition hover:bg-[#72F4E8] hover:text-[#032F57]"
							>
								Get Started
							</Button>
							<Link href="#features">
								<Button
									variant="outline"
									className="rounded-md border border-white bg-transparent px-4 py-2.5 text-sm font-medium text-white transition hover:bg-[#043B6DA6] hover:text-[#72F4E8]"
								>
									Explore Features
								</Button>
							</Link>
						</div>
					</div>
				</div>
				<div className="flex items-center justify-center">
					<div className="absolute -bottom-8 h-[162.06px] w-full max-w-[288px] rounded-2xl border border-white bg-[#032F57] shadow-[0px_4px_50px_0px_#00000040] sm:-bottom-10 sm:h-[207px] sm:max-w-[312px] md:-bottom-[60px] md:h-[310px] md:max-w-[552px] lg:h-[486.19px] lg:max-w-[864px] xl:-bottom-[81px] xl:h-[480px] xl:max-w-[853px]">
						<iframe
							className="h-full w-full rounded-2xl"
							src="https://www.youtube.com/embed/OKLJ6g8ba14?autoplay=1&mute=1&loop=1&playlist=OKLJ6g8ba14"
							title="Migranium Healthcare Platform"
							frameBorder="0"
							allow="accelerometer; autoplay; clipboard-write; encrypted-media; gyroscope; picture-in-picture"
							allowFullScreen
						></iframe>
					</div>
				</div>{" "}
			</div>
			<div className="flex items-center justify-center">
				<Button
					className="my-7 h-[52px] w-full max-w-[288px] whitespace-nowrap bg-[#053969] text-white ease-in-out hover:text-[#72F4E8] sm:max-w-[312px] md:max-w-[142px] lg:hidden"
					disabled={false}
				>
					Get Started{" "}
				</Button>{" "}
			</div>
		</section>
	);
};

export default HeroSection;
