import React, { useState, useEffect, useRef, useCallback } from "react";
import first from "../../../../public/assets/first.png";
import second from "../../../../public/assets/second.png";
import third from "../../../../public/assets/third.png";
import fourth from "../../../../public/assets/fourth.png";

export const Difference = () => {
	const [activeItem, setActiveItem] = useState("item-1");
	const [isInView, setIsInView] = useState(false);
	const accordionRef = useRef<HTMLDivElement>(null);
	const scrollTimeout = useRef<NodeJS.Timeout | null>(null);
	const isScrolling = useRef(false);

	const accordionData = [
		{
			id: "item-1",
			title: "Highly Adaptable to Your Unique Workflow",
			content:
				"Every practice operates differently. Migranium adapts to yours. Whether you're high-volume or highly specialized, easily customize settings to fit your specific workflow and preferences.",
			image: first.src,
		},
		{
			id: "item-2",
			title: "AI That Saves Time and Delivers Value",
			content:
				"Leverage artificial intelligence to streamline your processes and deliver exceptional value to your clients with automated workflows and intelligent insights.",
			image: second.src,
		},
		{
			id: "item-3",
			title: "Get Started In Days With No Downtime",
			content:
				"Quick implementation process that gets you up and running without disrupting your current operations or causing any downtime.",
			image: third.src,
		},
		{
			id: "item-4",
			title: "Manage Everything In One Connected Platform",
			content:
				"Centralize all your operations in a single, integrated platform that connects all aspects of your workflow for maximum efficiency.",
			image: fourth.src,
		},
	];

	const getCurrentItemIndex = () => {
		return accordionData.findIndex((item) => item.id === activeItem);
	};

	const isAtFirstItem = () => getCurrentItemIndex() === 0;
	const isAtLastItem = () =>
		getCurrentItemIndex() === accordionData.length - 1;

	const navigateToItem = useCallback(
		(direction: "up" | "down") => {
			const currentIndex = getCurrentItemIndex();
			let newIndex;

			if (direction === "down") {
				newIndex =
					currentIndex < accordionData.length - 1
						? currentIndex + 1
						: currentIndex;
			} else {
				newIndex = currentIndex > 0 ? currentIndex - 1 : currentIndex;
			}

			if (newIndex !== currentIndex) {
				setActiveItem(accordionData[newIndex].id);
				return true;
			}
			return false;
		},
		[accordionData, activeItem]
	);

	useEffect(() => {
		const observer = new IntersectionObserver(
			([entry]) => {
				setIsInView(entry.isIntersecting);
			},
			{
				threshold: 0.3,
				rootMargin: "-10% 0px -10% 0px",
			}
		);

		if (accordionRef.current) {
			observer.observe(accordionRef.current);
		}

		return () => {
			if (accordionRef.current) {
				observer.unobserve(accordionRef.current);
			}
		};
	}, []);

	useEffect(() => {
		const handleScroll = (e: WheelEvent) => {
			if (!isInView || !accordionRef.current?.contains(e.target as Node))
				return;

			const scrollDirection = e.deltaY > 0 ? "down" : "up";
			const shouldAllowNormalScroll =
				(scrollDirection === "up" && isAtFirstItem()) ||
				(scrollDirection === "down" && isAtLastItem());

			if (shouldAllowNormalScroll) {
				return;
			}

			e.preventDefault();

			if (scrollTimeout.current) {
				clearTimeout(scrollTimeout.current);
			}

			scrollTimeout.current = setTimeout(() => {
				if (isScrolling.current) return;

				isScrolling.current = true;
				navigateToItem(scrollDirection);

				setTimeout(() => {
					isScrolling.current = false;
				}, 10);
			}, 20);
		};

		window.addEventListener("wheel", handleScroll, { passive: false });

		return () => {
			window.removeEventListener("wheel", handleScroll);
			if (scrollTimeout.current) {
				clearTimeout(scrollTimeout.current);
			}
		};
	}, [isInView, navigateToItem]);

	const toggleItem = (itemId: string) => {
		setActiveItem(itemId === activeItem ? "" : itemId);
	};

	return (
		<section
			id="why-migranium"
			className="mb-[29px] py-[52px] font-inter lg:mb-[45px] lg:py-[96px]"
		>
			<div>
				<div className="mx-auto max-w-4xl text-center msm:max-w-[311px]">
					<span className="mb-2 font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
						What Makes Us Different
					</span>
					<h2 className="mb-[2px] text-xl font-bold text-[#0C0D0E] lg:text-[32px]">
						Built specifically for complex healthcare workflows{" "}
					</h2>
					<p className="text-sm text-[#68778D] lg:text-lg">
						Adapts effortlessly to any workflow, is easy to use,
						powered by AI to enhance how you work.
					</p>
				</div>
			</div>

			<div className="mx-auto mb-[45px] mt-12 flex w-full flex-col-reverse items-center justify-between font-inter md:max-w-[1216px] lg:flex-row">
				<div
					ref={accordionRef}
					className="flex w-full flex-col-reverse items-center justify-between md:mx-10 lg:flex-row"
				>
					{/* Left side*/}
					<div className="space-y-6">
						<div className="mx-auto w-full px-3 md:px-0">
							{accordionData.map((item, index) => (
								<div key={item.id} className="">
									<button
										aria-expanded={activeItem === item.id}
										aria-controls={`content-${item.id}`}
										onClick={() => toggleItem(item.id)}
										className={`flex w-full items-start border-l-4 pl-2 text-left outline-none transition-all duration-200 sm:pr-4 md:pl-6 ${
											activeItem === item.id
												? "border-teal-500"
												: "border-gray-300"
										}`}
									>
										<div className="flex w-full items-start">
											<div
												className={`flex-1 ${activeItem === item.id ? "text-[#0C0D0E]" : "text-[#0C0D0E]/40"} ${index !== accordionData.length - 1 ? "mb-6" : ""}`}
											>
												<h3
													className={`text-base font-semibold leading-tight transition-colors duration-200 lg:text-lg`}
												>
													{item.title}
												</h3>
												{activeItem === item.id &&
													item.content && (
														<div className="mt-3 max-w-[497px] text-sm leading-relaxed text-[#303741] md:text-base">
															{item.content}
														</div>
													)}
											</div>
										</div>
									</button>
								</div>
							))}
						</div>
					</div>

					{/* Right side*/}
					<div className="flex justify-center lg:justify-end">
						<div className="relative">
							<div className="flex h-[260px] w-[260px] items-center justify-center overflow-hidden md:h-[350px] md:w-[350px] lg:h-[467px] lg:w-[467px]">
								{activeItem === "" && (
									<div className="absolute inset-0 flex items-center justify-center">
										<div className="relative h-full w-full">
											<img
												src={first.src}
												alt="Default interface"
												className="rounded-full object-cover"
											/>
										</div>
									</div>
								)}
								{accordionData.map((item) => (
									<div
										key={item.id}
										className={`absolute inset-0 flex items-center justify-center transition-opacity duration-500 ${
											activeItem === item.id
												? "opacity-100"
												: "opacity-0"
										}`}
									>
										<div className="relative h-full w-full">
											<img
												src={
													item.image ||
													"/placeholder.svg"
												}
												alt={`Interface for ${item.title}`}
												className="rounded-full object-cover"
											/>
										</div>
									</div>
								))}
							</div>
						</div>
					</div>
				</div>
			</div>
		</section>
	);
};
