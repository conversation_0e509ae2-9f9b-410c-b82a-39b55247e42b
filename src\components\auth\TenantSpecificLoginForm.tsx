"use client";

import { useState, FormEvent } from "react";
import Image from "next/image";
import { TenantConfig } from "@/types/tenant";
import { Label } from "@/components/ui/label";
import { Input } from "@/components/ui/input";
import InputIcon from "@/components/ui-extended/input-icon";
import { LoaderButton } from "@/components/ui-extended/loader-button";
import Checkbox from "@/components/ui-extended/checkbox";
import SigninWIthGoogle from "@/components/Signup/google/SigninWIthGoogle";
import SignInWithMicrosoft from "@/components/Signup/azure/SignInWithMicrosoft";
import { Key } from "lucide-react";
import { LuEye, LuEyeOff } from "react-icons/lu";
import Link from "next/link";
import useUserStore from "@/store/useUserStore";
import VerifyEmail from "@/components/Onboarding/VerifyEmail";
import CreateAccountOnSSO from "@/components/Signup/CreateAccountOnSSO";
import { useLoginUser } from "@/store/slices/auth";
import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";

interface TenantSpecificLoginFormProps {
	tenantConfig: TenantConfig;
	onSwitchToStandard: () => void;
}

export default function TenantSpecificLoginForm({
	tenantConfig,
	onSwitchToStandard,
}: TenantSpecificLoginFormProps) {
	const [email, setEmail] = useState("");
	const [password, setPassword] = useState("");
	const [passwordType, setPasswordType] = useState("password");
	const [error, setError] = useState<string | null>(null);
	const [showVerifyEmailModal, setShowVerifyEmailModal] = useState(false);
	const [showCreateAccountOnSSO, setShowCreateAccountOnSSO] = useState(false);
	const [SSOToken, setSSOToken] = useState<{
		type: "microsoft" | "google";
		token: string | null;
	} | null>(null);

	const loginUserMutation = useLoginUser(setError);
	const { setMfaUser, setUser, rememberAuth, setRememberAuth } = useUserStore(
		(s) => ({
			rememberAuth: s.rememberAuth,
			setRememberAuth: s.setRememberAuth,
			setMfaUser: s.setMfaUser,
			setUser: s.setUser,
			reset: s.reset,
		})
	);
	const handleLoginSuccess = useHandleLoginSuccess();

	const handleSubmit = async (e: FormEvent) => {
		e.preventDefault();
		setError(null);

		try {
			setMfaUser(null);
			setUser(null);
			loginUserMutation.mutate(
				{
					email: email,
					password: password,
					remember_me: rememberAuth?.rememberMe ?? false,
					remember_token:
						rememberAuth?.rememberToken &&
						rememberAuth?.rememberToken,
				},
				{
					onSuccess: (data) => {
						handleLoginSuccess(data);
						setShowVerifyEmailModal(
							Boolean(
								!("twoFactor" in data.data) &&
									!data.data.is_email_verified
							)
						);
					},
				}
			);
		} catch (error) {
			setError("An error occured kindly try again later");
		}
	};

	const handleSSOLogin = async () => {
		if (!tenantConfig.ssoLoginUrl) {
			setError("SSO login URL not configured");
			return;
		}
		window.location.href = tenantConfig.ssoLoginUrl;
	};

	return (
		<div className="flex flex-col items-center justify-center gap-y-6">
			<h3 className="text-[32px] font-semibold leading-[30px] tracking-[-0.22px] text-[#323539]">
				Welcome to {tenantConfig.name}&apos;s Admin Portal
			</h3>
			<div className="relative z-10 flex w-full max-w-[488px] flex-col space-y-6 overflow-hidden rounded-[10px] bg-white pt-6 shadow-[0_20px_25px_-5px_rgba(16,24,40,0.1),_0_8px_10px_-6px_rgba(16,24,40,0.1)]">
				{/* <div className="flex flex-col space-y-2 px-8">
					{tenantConfig.customLoginMessage ? (
						<p className="font-normal tracking-[-1%] text-[#858C95]">
							{tenantConfig.customLoginMessage}
						</p>
					) : (
						<a
							href={LANDING_ENVIRONMENT_LINK + "/sign-up"}
							className="font-normal tracking-[-1%] text-[#858C95]"
						>
							Don&apos;t have an account?{" "}
							<span className="text-[#195388]">Sign up</span>
						</a>
					)}
				</div> */}

				<div className="flex flex-col items-center gap-y-2">
					{tenantConfig.logo && (
						<div className="flex justify-center px-8">
							<Image
								src={tenantConfig.logo}
								alt={`${tenantConfig.name} logo`}
								width={150}
								height={50}
							/>
						</div>
					)}
					<p className="text-lg font-medium text-main-1">
						Log in using {tenantConfig.name}&apos;s credentials.
					</p>
				</div>

				<form
					onSubmit={handleSubmit}
					className="flex flex-col space-y-6"
				>
					{tenantConfig.showPasswordLogin && (
						<div className="flex flex-col space-y-6 px-8">
							<div className="space-y-1.5">
								<Label
									htmlFor="email"
									className="text-[#323539]"
								>
									Email Address{" "}
									<span className="text-[#c9312c]">*</span>
								</Label>
								<Input
									id="email"
									type="email"
									value={email}
									onChange={(e) => setEmail(e.target.value)}
									max={254}
									placeholder="Enter your email address"
									required
								/>
							</div>

							<div className="space-y-1.5">
								<Label
									htmlFor="password"
									className="text-[#323539]"
								>
									Password{" "}
									<span className="text-[#c9312c]">*</span>
								</Label>
								<InputIcon
									id="password"
									type={passwordType}
									value={password}
									onChange={(e) =>
										setPassword(e.target.value)
									}
									outerClassName="flex flex-row-reverse"
									placeholder="Enter your password"
									required
									bigIcon={
										passwordType === "password" ? (
											<LuEyeOff
												onClick={() =>
													setPasswordType(
														passwordType ===
															"password"
															? "text"
															: "password"
													)
												}
												className="cursor-pointer text-main-1"
											/>
										) : (
											<LuEye
												onClick={() =>
													setPasswordType(
														passwordType ===
															"password"
															? "text"
															: "password"
													)
												}
												className="cursor-pointer text-main-1"
											/>
										)
									}
								/>
							</div>
						</div>
					)}

					{error && (
						<p className="px-8 text-sm tracking-[-0.1px] text-red-500">
							{error}
						</p>
					)}

					{tenantConfig.showPasswordLogin && (
						<div className="flex items-center justify-between space-x-3 px-8">
							<div
								className="flex items-center space-x-1.5"
								onClick={() =>
									setRememberAuth({
										...rememberAuth,
										rememberMe: !rememberAuth?.rememberMe,
									})
								}
							>
								<Checkbox
									handleCheckboxChange={() =>
										setRememberAuth({
											...rememberAuth,
											rememberMe:
												!rememberAuth?.rememberMe,
										})
									}
									isChecked={rememberAuth?.rememberMe}
									id="remember-me"
									className="h-4 w-4 rounded-sm border-[#323539]"
								/>
								<Label>Remember me for the next 30 days</Label>
							</div>
							<Link
								href={"/forgot-password"}
								className="font-normal tracking-[-1%] underline"
							>
								Forgot Password?
							</Link>
						</div>
					)}

					<div className="flex flex-col items-stretch space-y-4 bg-[#FAFBFC] px-8 pb-4 pt-[18px]">
						{tenantConfig.showPasswordLogin && (
							<LoaderButton
								disabled={loginUserMutation.isPending}
								loading={loginUserMutation.isPending}
								loaderSize={20}
								className="h-10 w-full text-white"
								type="submit"
							>
								Sign in
							</LoaderButton>
						)}

						<p className="text-center text-sm text-[#858C95]">
							{tenantConfig.showPasswordLogin
								? "Or Sign in With"
								: "Sign in With"}
						</p>

						<div className="flex sm:space-x-2 msm:flex-col msm:space-y-2">
							{!tenantConfig.showGoogleLogin && (
								<SigninWIthGoogle
									type="sign-in"
									setSSOToken={setSSOToken}
									setShowCreateAccountOnSSO={
										setShowCreateAccountOnSSO
									}
								/>
							)}

							{!tenantConfig.showMicrosoftLogin && (
								<SignInWithMicrosoft
									type="sign-in"
									setSSOToken={setSSOToken}
									setShowCreateAccountOnSSO={
										setShowCreateAccountOnSSO
									}
								/>
							)}

							{tenantConfig.showSSOLogin && (
								<button
									type="button"
									onClick={handleSSOLogin}
									className="flex h-10 w-full items-center justify-center gap-x-2 rounded-md border border-[#E9EAEB] bg-white px-3 text-sm font-medium text-[#323539] hover:bg-gray-50 md:w-fit"
								>
									<Key
										size={14}
										className="rotate-[-270] scale-x-[-1]"
									/>
									{tenantConfig.ssoButtonText || "SSO"}
								</button>
							)}
						</div>

						<div className="mt-4 text-center">
							<p className="text-[#858C95]">
								Not a {tenantConfig.name} member?{" "}
								<button
									type="button"
									className="font-normal tracking-[-1%] text-[#195388]"
									onClick={onSwitchToStandard}
								>
									Sign in with email
								</button>
							</p>
						</div>
					</div>
				</form>

				{/* <RequestIsLoading isWhite size={20} isLoading={isLoading} /> */}

				<VerifyEmail
					email={email}
					showVerifyEmailModal={showVerifyEmailModal}
					setShowVerifyEmailModal={setShowVerifyEmailModal}
				/>

				<CreateAccountOnSSO
					SSOToken={SSOToken}
					showCreateAccountOnSSO={showCreateAccountOnSSO}
					setShowCreateAccountOnSSO={setShowCreateAccountOnSSO}
				/>
			</div>
		</div>
	);
}
