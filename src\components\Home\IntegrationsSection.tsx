import React from "react";
// Assuming you have imported your logos like this:
// import epicLogo from 'path/to/epic-logo.png';
// import oscarProLogo from 'path/to/oscarpro-logo.png';
// import accuroLogo from 'path/to/accuro-logo.png';
// import cernerLogo from 'path/to/cerner-logo.png';
// import oracleCernerLogo from 'path/to/oracle-cerner-logo.png';
// import openEmrLogo from 'path/to/openemr-logo.png';
// import junoLogo from 'path/to/juno-logo.png';

const integrationLogos = [
	{ src: "path/to/epic-logo.png", alt: "Epic" },
	{ src: "path/to/oscarpro-logo.png", alt: "OSCAR Pro" },
	{ src: "path/to/accuro-logo.png", alt: "Accuro" },
	{ src: "path/to/cerner-logo.png", alt: "Cerner" },
	{ src: "path/to/oracle-cerner-logo.png", alt: "Oracle Cerner" },
	{ src: "path/to/openemr-logo.png", alt: "openEMR" },
	{ src: "path/to/juno-logo.png", alt: "Juno" },
];

export const IntegrationsSection = () => {
	return (
		<section className="flex w-full flex-col items-center justify-center bg-white px-4 py-20">
			<div className="mx-auto flex w-full max-w-4xl flex-col items-center text-center">
				<span className="mb-2 text-base font-semibold text-[#1AC19D]">
					Integrations
				</span>
				<h2 className="mb-2 text-2xl font-bold text-[#23272E] sm:text-3xl md:text-4xl">
					Seamless Integration with Your EMR and Tools
				</h2>
				<p className="mb-10 text-base text-[#8CA3B6] sm:text-lg">
					Quick setup, zero downtime, and smooth integration with your
					existing systems.
				</p>
			</div>
			<div className="mx-auto flex w-full max-w-6xl flex-wrap items-center justify-center gap-8">
				{integrationLogos.map((logo, idx) => (
					<img
						key={idx}
						src={logo.src}
						alt={logo.alt}
						className="h-8 w-auto opacity-80 grayscale"
						// Adjust max-width or height if needed to match Figma
					/>
				))}
			</div>
		</section>
	);
};
