import { z } from "zod";

export const AddLocationSchema = z.object({
	name: z.string().min(3, { message: "Location name is required" }),
	address: z.string(),
	image: z.any(),
	country: z.string(),
	state: z.string(),
	city: z.string(),
	approximate_waiting_time: z.string().refine(
		(time) => {
			return +time >= 5;
		},
		{
			message: "Minimum wait time: 5 mins",
		}
	),
	schedule_block_in_min: z
		.number()
		.min(5, "Minimum schedule block time: 5 mins"),
	time_zone: z.string().refine((value) => !!value, {
		message: "Kindly select time zone",
	}),
	day_time_slots: z.array(
		z.object({
			day: z.string(),
			day_value: z.number(),
			is_active: z.number(),
			time_slots: z.array(
				z.object({
					start_time: z.string(),
					end_time: z.string(),
				})
			),
		})
	),
});

export const AddBusinessInfoSchema = z.object({
	name: z.string().min(4, { message: "Name must be at least 4 characters" }),
	address: z
		.string()
		.min(4, { message: "Address must be at least 4 characters" }),
	country: z.string(),
	state: z.string(),
	city: z.string(),
	phone_number: z.string().optional(),
	business_category_id: z
		.string()
		.min(1, { message: "Kindly select a business category" }),
	zip_code: z.string().optional(),
	product_type: z.string(),
	logo: z.any().optional(),
});

export const UpdateBusinessCardSchema = z.object({
	payment_information: z.object({
		name: z
			.string()
			.min(6, { message: "Name must be at least 6 characters" })
			.optional(),
		zipCode: z
			.string()
			.min(6, { message: "Zip Code must be at least 6 characters" })
			.optional(),
		type: z.string(),
		card: z.object({
			number: z
				.string()
				.min(18, { message: "This card number is too short" }),
			exp_month: z
				.number()
				.min(1, { message: "Expiration date is required" }),
			exp_year: z
				.number()
				.min(1, { message: "Expiration date is required" }),
			cvc: z.string().min(3, { message: "CVC is required" }),
		}),
	}),
});

export const AddBusinessCardSchema = UpdateBusinessCardSchema.extend({
	price_id: z.string(),
});

export const timeSlotSchema = z.object({
	id: z.number().optional(),
	start_time: z.string(),
	end_time: z.string(),
	is_active: z.number().int().optional(),
	product_working_hour_id: z.number().optional(),
	created_at: z.string().optional(),
	updated_at: z.string().optional(),
});

const TimeSlotSchema = z.object({
	start_time: z.string(),
	end_time: z.string(),
});

const dayDataSchema = z.object({
	is_active: z.boolean(),
	time_slots: z.array(timeSlotSchema),
});

export const scheduleSettingSchema = z.object({
	join_from_qr: z.number().optional(),
	join_from_url: z.number().optional(),
	schedule_block_in_minute: z.number(),
	is_visible: z.number().optional(),
	schedule_block_away_count: z.number().optional(),
	schedule_block_away_option: z.enum(["days", "weeks", "months"]).optional(),
	auto_approve: z.number().optional(),
	is_open: z.number().optional(),
	qr_code_url: z.string().optional(),
	join_url: z.string().optional(),
});

export const scheduleOptimizerSchema = z.object({
	is_active: z.number().optional(),
	join_from_url: z.number().optional(),
	join_from_qr: z.number().optional(),
	waitlist_selected: z.number().optional(),
	waitlist_number_of_people: z.number().optional(),
	high_priority_selected: z.number().optional(),
	high_priority_number_of_people: z.number().optional(),
	upcoming_appointments_selected: z.number().optional(),
	upcoming_appointments_number_of_people: z.number().optional(),
	upcoming_appointments_limit: z.number().optional(),
	upcoming_appointments_limit_options: z
		.enum(["days", "weeks", "months"])
		.optional(),
});

export const waitlistSettingSchema = z.object({
	join_from_url: z.number().optional(),
	join_from_qr: z.number().optional(),
	auto_clearing: z.number().optional(),
	auto_clearing_minute: z.number().optional(),
	auto_flow: z.number().optional(),
	manual_wait_time: z.number().optional(),
	use_average_time: z.number().optional(),
	is_queue_active: z.number().optional(),
	is_visible: z.number().optional(),
});

export const AddBusinessOperatingHoursSchema = z.object({
	apply_to_option: z
		.object({
			apply_to_all: z.number(), // 0 means locations array must have elements
			locations: z
				.array(
					z
						.object({
							id: z.number().optional(),
							update_location: z.number().optional(),
							apply_to_all_stations: z.number().optional(),
							selected_stations: z.array(z.number()).optional(),
						})
						.optional()
				)
				.optional(),
		})
		.optional()
		.refine(
			(data) =>
				data?.apply_to_all === 1 ||
				(data?.locations && data.locations.length > 0),
			{
				message:
					"Kindly select at least one location or select apply to all",
				path: [""],
			}
		)
		.optional(),
	working_hours_data: z.object({
		waitlist_schedule_option: z.enum(["all", "schedule", "waitlist"]),
		working_hours: z.record(dayDataSchema),
	}),
	queue_settings: z
		.object({
			time_zone: z.string(),
			auto_approve: z.number().optional(),
			is_visible: z.number().optional(),
			waitlist_setting: waitlistSettingSchema.optional(),
			schedule_setting: scheduleSettingSchema,
			schedule_optimizer: scheduleOptimizerSchema.optional(),
		})
		.optional(),
});

export const operatingHourSchema = z.object({
	id: z.number().optional(),
	day: z.string().optional(),
	day_value: z.number().optional(),
	day_of_week: z.string().optional(),
	workable_id: z.number().optional(),
	workable_type: z.string().optional(),
	is_active: z.union([z.literal(0), z.literal(1)]),
	created_at: z.string().optional(),
	updated_at: z.string().optional(),
	time_slots: z.array(timeSlotSchema),
	service_time_slots: z.any(),
});

export interface TimeSlot {
	is_active?: 0 | 1;
	start_time: string;
	end_time: string;
}

export interface DaySchedule {
	is_active: boolean;
	time_slots: TimeSlot[];
}

export interface ScheduleData {
	[key: string]: DaySchedule;
}

export type TimeZoneInfo = {
	id: string; // The IANA timezone identifier
	country: string; // The name of the country extracted from the timezone string
	offsetHours: number; // The UTC offset in hours
};

export type ContinentTimeZones = {
	continent: string; // The name of the continent
	times: TimeZoneInfo[]; // An array of time zones within this continent
};

export interface SubscriptionPlan {
	product_id: string;
	name: string;
	description: string;
	features: string[];
	price: number;
	trial_period_days: number | null;
	default_price: {
		price_id: string;
		active: boolean;
		price: number;
		features: string[];
	};
	custom_prices: any[];
}

export type AddBusinessOperatingHoursData = z.infer<
	typeof AddBusinessOperatingHoursSchema
>;
export type AddBusinessCardData = z.infer<typeof AddBusinessCardSchema>;
export type UpdateBusinessCardData = z.infer<typeof UpdateBusinessCardSchema>;
export type AddBusinessInfoData = z.infer<typeof AddBusinessInfoSchema>;
export type AddLocationType = z.infer<typeof AddLocationSchema>;
export type OperatingHour = z.infer<typeof operatingHourSchema>;

export const businessDetailsSchema = z.object({
	name: z.string().min(1, {
		message: "Business name is required",
	}),
	address: z.string().min(1, {
		message: "Address is required",
	}),
	country: z.string().min(1, {
		message: "Country is required",
	}),
	state: z.string().min(1, {
		message: "State is required",
	}),
	city: z.string().optional(),
	phone_number: z
		.string()
		.min(10, {
			message: "Phone number is required",
		})
		.max(10),
	zip_code: z.string().optional(),
	products: z.array(z.string()).min(1, {
		message: "Products are required",
	}),
	business_category_id: z.number().positive(),
	logo: z.string().optional(),
});

export const workingHoursSchema = z.record(
	z.object({
		is_active: z.boolean(),
		time_slots: z.array(timeSlotSchema),
	})
);

export const queueSettingsSchema = z.object({
	estimated_wait_time: z.number(),
	schedule_block_in_minutes: z.number(),
	time_zone: z.string(),
});

export const onboardingFormSchema = z.object({
	working_hours: workingHoursSchema,
	queue_settings: queueSettingsSchema,
	business_details: businessDetailsSchema,
});

export type OnboardingFormType = z.infer<typeof onboardingFormSchema>;

export interface OnboardingSuccessResponse {
	status: true;
	message: "Onboarding data saved.";
	data: object;
}

export interface OnboardingErrorResponse {
	status: false;
	message: "Failed to save onboarding data.";
	errors: Record<string, string[]>;
}

export interface OnboardingImageUploadSuccessResponse {
	status: true;
	message: "Onboarding data saved.";
	data: { image_url: string };
}
