"use client";
import { GoogleOauthLoginSlice } from "@/store/slices/signin/googleOauthLoginSlice";
import { MicrosoftOatuhRegisterSlice } from "@/store/slices/signin/microsoftOatuhLoginSlice";
import React from "react";
import useCustomToast from "../CustomToast";
import { LoaderButton } from "../ui-extended/loader-button";
import { Button } from "../ui/button";
import { Dialog, DialogContent, DialogFooter, DialogTitle } from "../ui/dialog";

const CreateAccountOnSSO: React.FC<{
	SSOToken: {
		type: "microsoft" | "google";
		token: string | null;
	} | null;
	showCreateAccountOnSSO: boolean;
	setShowCreateAccountOnSSO: React.Dispatch<React.SetStateAction<boolean>>;
}> = ({ SSOToken, showCreateAccountOnSSO, setShowCreateAccountOnSSO }) => {
	const microsoftRegisterMutation = MicrosoftOatuhRegisterSlice();
	const googleOauthLoginMutation = GoogleOauthLoginSlice();
	const customToast = useCustomToast();
	const onSubmitForm = () => {
		if (SSOToken?.type === "microsoft" && SSOToken.token)
			microsoftRegisterMutation.mutate(
				{
					token: SSOToken.token,
				},
				{
					onSuccess: () => {
						customToast("Account created successfully 🎉", {
							id: "microsoft-signup",
						});
					},
				}
			);

		if (SSOToken?.type === "google" && SSOToken.token)
			googleOauthLoginMutation.mutate(
				{
					token: SSOToken.token,
					type: "sign-up",
				},
				{
					onSuccess: () => {
						customToast("Account created successfully 🎉", {
							id: "google-signup",
						});
					},
				}
			);
	};

	return (
		<Dialog
			open={showCreateAccountOnSSO}
			onOpenChange={setShowCreateAccountOnSSO}
		>
			<DialogContent className="max-w-[360px] p-4">
				<form
					className="flex flex-col space-y-4"
					onSubmit={onSubmitForm}
				>
					<DialogTitle className="text-[22px] font-semibold capitalize leading-[30px] -tracking-[1%] text-[#323539]">
						Create Account
					</DialogTitle>
					<p className="trakcing-[-0.1px] text-[14px] leading-[20px] text-[#6D748D]">
						This email is not registered with us. Would you like to
						create an account?
					</p>
					<DialogFooter className="">
						<Button
							type="button"
							variant="outline-primary"
							className="flex-1"
						>
							Cancel
						</Button>
						<LoaderButton
							className="flex-1 self-end text-white"
							type="button"
							loaderSize={20}
							onClick={onSubmitForm}
							loading={
								microsoftRegisterMutation.isPending ||
								googleOauthLoginMutation.isPending
							}
							disabled={
								microsoftRegisterMutation.isPending ||
								googleOauthLoginMutation.isPending
							}
						>
							Create
						</LoaderButton>
					</DialogFooter>
				</form>
			</DialogContent>
		</Dialog>
	);
};

export default CreateAccountOnSSO;
