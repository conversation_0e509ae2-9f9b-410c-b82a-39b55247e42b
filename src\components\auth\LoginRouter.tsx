"use client";

import { useEffect, useState } from "react";
import { TenantConfig } from "@/types/tenant";
import StandardLoginForm from "./StandardLoginForm";
import SSOLoginForm from "./SSOLoginForm";
import TenantSpecificLoginForm from "./TenantSpecificLoginForm";
type LoginView = "standard" | "sso" | "tenant-specific";

interface LoginRouterProps {
	tenantConfig: TenantConfig;
}

const removeSubdomain = (url: string): string => {
	const urlObj = new URL(url);
	const hostParts = urlObj.hostname.split(".");
	if (
		hostParts.length >= 2 &&
		hostParts[hostParts.length - 1] === "localhost"
	) {
		urlObj.hostname = "localhost";
		return urlObj.toString();
	}
	const specialTLDs = [
		"co.uk",
		"com.au",
		"co.jp",
		"org.uk",
		"gov.uk",
		"ac.uk",
		"edu.au",
	];

	if (hostParts.length > 2) {
		const potentialSpecialTLD = hostParts.slice(-2).join(".");
		if (specialTLDs.includes(potentialSpecialTLD) && hostParts.length > 3) {
			urlObj.hostname = hostParts.slice(-3).join(".");
		} else {
			urlObj.hostname = hostParts.slice(-2).join(".");
		}
	}

	return urlObj.toString();
};

export default function LoginRouter({ tenantConfig }: LoginRouterProps) {
	const [currentView, setCurrentView] = useState<LoginView>(
		tenantConfig.loginType == "custom" ? "tenant-specific" : "standard"
	);
	const goToSSO = () => setCurrentView("sso");
	const goToStandard = () => {
		setCurrentView("standard");
		const currentUrl = window.location.href;
		const baseUrl = removeSubdomain(currentUrl);
		window.location.href = baseUrl;
	};

	useEffect(() => {
		const updatedView: LoginView =
			tenantConfig.loginType == "custom" ? "tenant-specific" : "standard";
		setCurrentView(updatedView);
	}, [tenantConfig]);

	return (
		<div className="">
			{currentView === "standard" && (
				<StandardLoginForm
					tenantConfig={tenantConfig}
					onSwitchToSSO={goToSSO}
				/>
			)}

			{currentView === "sso" && (
				<SSOLoginForm
					tenantConfig={tenantConfig}
					onBack={goToStandard}
				/>
			)}

			{currentView === "tenant-specific" && (
				<TenantSpecificLoginForm
					tenantConfig={tenantConfig}
					onSwitchToStandard={goToStandard}
				/>
			)}
		</div>
	);
}
