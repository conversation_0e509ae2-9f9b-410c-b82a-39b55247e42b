import { useHandleLoginSuccess } from "@/hooks/useLoginSuccess";
import { APIVersion2GoogleOauthLogin } from "@/http/v2";
import { AuthLoginResponse } from "@/types/sign-in";
import { AuthTwoEnabledFactorResponse, User } from "@/types/signup";
import { useMutation } from "@tanstack/react-query";
import { AxiosError } from "axios";

export const GoogleOauthLoginSlice = () => {
	const handleLoginSuccess = useHandleLoginSuccess();

	return useMutation<
		AuthLoginResponse | AuthTwoEnabledFactorResponse,
		AxiosError<any>,
		{
			token: string;
			type: "sign-in" | "sign-up";
		}
	>({
		mutationFn: APIVersion2GoogleOauthLogin,
		onSuccess: (data) => {
			handleLoginSuccess(data);
		},
	});
};
