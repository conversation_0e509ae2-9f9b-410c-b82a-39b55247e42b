"use client";
import { CONFIGS } from "@/configs";
import Intercom from "@intercom/messenger-js-sdk";
import { useEffect } from "react";

const useHelpIntercomIdentify: () => void = () => {
	const handleIntercomIdentify = () => {
		Intercom({
			app_id: CONFIGS.INTERCOM.INTERCOM_APP_ID ?? "",
		});
	};

	useEffect(() => {
		handleIntercomIdentify();
	}, []);
};

export default useHelpIntercomIdentify;
