import React, { Dispatch, SetStateAction, useEffect, useState } from "react";
import { Button } from "../ui/button";

export const GetInTouch: React.FC<{
	setshowContactUsModal: Dispatch<SetStateAction<boolean>>;
}> = ({ setshowContactUsModal }) => {
	const [_, setRootElement] = useState<null | HTMLElement>(null);

	useEffect(() => {
		setRootElement(document.getElementById("root"));
	}, []);
	return (
		<>
			<div className="mx-auto flex w-full flex-col items-center py-16 text-center font-inter md:max-w-2xl lg:py-24 msm:max-w-[314px]">
				<p className="mb-2 font-shantell text-sm font-medium text-[#01B18B] md:mb-4 lg:text-lg">
					Get In Touch With Us
				</p>
				<h2 className="text-xl font-bold md:mb-6 lg:text-[32px]">
					Ready to optimize your operations?
				</h2>
				<p className="mb-6 text-sm text-[#68778D] md:mb-8 lg:text-lg">
					Unlock growth and efficiency with Migranium. Take the first
					step towards operational excellence. Book a demo or contact
					us now.
				</p>
				<Button
					disabled={false}
					onClick={() => {
						setshowContactUsModal(true);
					}}
					className="h-[52px] rounded-md bg-[#032F57] text-sm font-medium text-white md:h-10 md:w-[107px] msm:w-full"
				>
					Contact Us
				</Button>
			</div>
		</>
	);
};
