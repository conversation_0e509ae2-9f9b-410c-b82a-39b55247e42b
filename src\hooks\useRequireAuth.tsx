"use client";

import useUserStore from "@/store/useUserStore";
import { usePathname, useRouter, useSearchParams } from "next/navigation";
import React from "react";

const RequireAuth: React.FC = () => {
	const router = useRouter();
	const path = usePathname();
	// const customToast = useCustomToast();

	const { user } = useUserStore((s) => ({
		user: s.user,
		mfaUser: s.mfaUser,
	}));

	React.useEffect(() => {
		const isOnboardingRoute = path === "/onboarding";
		if (!isOnboardingRoute) {
			if (!user) {
				// if (!mfaUser && !user) {
				// 	console.log(1);
				// 	router.replace("/sign-in");
				// }
				// console.log(2);
				router.replace("/sign-in");
			}

			if (path.includes("/onboarding") && user?.businesses?.length) {
				// console.log(3);
				router.replace("/sign-in");
			}
		}

		// if (path.includes("/2fa") && user) router.replace("/sign-in");
	}, [user]);

	return null;
};

export default RequireAuth;
