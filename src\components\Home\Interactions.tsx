import epic from "../../../public/assets/images/epic.svg.png";
import oscar from "../../../public/assets/images/oscar.png";
import accuro from "../../../public/assets/images/accuro.png";
import cerner from "../../../public/assets/images/cerner.png";
import oracle from "../../../public/assets/images/oracle.png";
import open from "../../../public/assets/images/open-emr.png";
import juno from "../../../public/assets/images/juno.png";

const integrationLogos = [
	{ src: epic.src, alt: "Epic", height: 30 },
	{ src: oscar.src, alt: "OSCAR Pro", height: 30 },
	{ src: accuro.src, alt: "Accuro", height: 40 },
	{ src: cerner.src, alt: "Cerner", height: 30 },
	{ src: oracle.src, alt: "Oracle Cerner", height: 30 },
	{ src: open.src, alt: "openEMR", height: 29.89 },
	{ src: juno.src, alt: "Juno", height: 30 },
];

export const Interactions = () => {
	return (
		<section
			id="integrations"
			className="mb-[24px] flex w-full flex-col items-center justify-center px-4 pb-5 pt-[52px] font-inter lg:mb-[47px] lg:pb-[96px] lg:pt-[59px]"
		>
			<div className="mx-auto flex w-full max-w-4xl flex-col items-center text-center msm:max-w-[312px]">
				<span className="mb-2 font-shantell text-sm font-medium text-[#01B18B] lg:text-lg">
					Integrations
				</span>
				<h2 className="mb-2 text-xl font-bold text-[#0C0D0E] lg:text-[32px]">
					Seamless integration with your emr and tools{" "}
				</h2>
				<p className="mb-[52px] text-sm text-[#68778D] lg:text-lg">
					Quick setup, zero downtime, and smooth integration with your
					existing systems.
				</p>
			</div>
			<div className="mx-auto flex w-full max-w-[706px] flex-wrap items-center justify-center gap-[31px] lg:gap-[52px] msm:max-w-[282px]">
				{integrationLogos.map((logo, idx) => (
					<img
						key={idx}
						src={logo.src}
						alt={logo.alt}
						style={{ height: `${logo.height}px` }}
						className="w-auto"
					/>
				))}
			</div>
		</section>
	);
};
