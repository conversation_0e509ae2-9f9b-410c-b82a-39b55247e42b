"use client";

import AddLocationTimer from "@/components/Onboarding/AddLocationTimer";
import DefaultOperatingHoursModal from "@/components/Onboarding/DefaultOperatingHoursModal";
import { TimeZoneCustomSelect } from "@/components/Onboarding/TimeZoneSelect";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import {
	Select,
	SelectContent,
	SelectGroup,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/ui/select";
import {
	Tooltip,
	TooltipContent,
	TooltipProvider,
	TooltipTrigger,
} from "@/components/ui/tooltip";
import { cn } from "@/lib/utils";
import { NewOnboardingSlice } from "@/store/slices/onboarding";
import useUserStore from "@/store/useUserStore";
import { OnboardingFormType, OperatingHour } from "@/types/onboarding";
import { defaultTimeSlots } from "@/utils/constants";
import {
	convertSchedule,
	findCountry,
	findState,
	handleApiErrors,
	scheduleBlockOptions,
} from "@/utils/general";
import React, { useState } from "react";
import { SubmitHandler, useFormContext } from "react-hook-form";
import { LoaderButton } from "../ui-extended/loader-button";
import useCustomToast from "../CustomToast";
import { useRouter } from "next/navigation";

const AddOperatingHours: React.FC<{ countryCode: string }> = ({
	countryCode,
}) => {
	const customToast = useCustomToast();
	const router = useRouter();
	const NewOnboardingMutation = NewOnboardingSlice();
	const [timezone, setTimezone] = useState<string>(
		Intl.DateTimeFormat().resolvedOptions().timeZone
	);
	const { onboardingState, setOnboardingState } = useUserStore((s) => ({
		user: s.user,
		onboardingState: s.onboardingState,
		setOnboardingState: s.setOnboardingState,
	}));
	const formMethods = useFormContext<OnboardingFormType>();

	const onSubmitForm: SubmitHandler<OnboardingFormType> = (data) => {
		NewOnboardingMutation.mutate(
			{
				...data,
				business_details: {
					...data.business_details,
					phone_number:
						countryCode + data.business_details.phone_number,
					country: findCountry(data.business_details.country),
					state: findState(
						data.business_details.state,
						data.business_details.country
					),
				},
			},
			{
				onSuccess: () => {
					customToast("Onboarding Complete 🎉", {
						id: "onboarding",
						type: "success",
					});
					router.push("/2fa/verify");
				},
				onError: (error) => {
					customToast("Invalid details 🤕", {
						id: "onboarding",
						type: "error",
					});
					if (error.response?.data.errors)
						handleApiErrors(
							error.response?.data.errors,
							formMethods.setError
						);
					setOnboardingState(1);
				},
			}
		);
	};
	const [slots, setSlots] = useState<OperatingHour[]>(defaultTimeSlots);

	React.useEffect(
		() => formMethods.setValue("queue_settings.time_zone", timezone),
		[timezone]
	);

	React.useEffect(
		() => setTimezone(Intl.DateTimeFormat().resolvedOptions().timeZone),
		[]
	);

	React.useEffect(() => {
		formMethods.setValue("working_hours", convertSchedule(slots));
	}, [slots]);

	return (
		<>
			<div
				className={cn(
					"relative mb-5 h-fit max-h-fit w-full max-w-[656px] flex-col space-y-4 rounded-[10px] bg-white shadow-[0px_20px_25px_-5px_rgba(16,24,40,0.10),0px_8px_10px_-6px_rgba(16,24,40,0.10)]",
					{
						hidden: onboardingState === 1,
						flex: onboardingState === 2,
					}
				)}
			>
				<div className="px-8 py-3">
					<h2
						className="text-[22px] font-semibold text-[#323539]"
						onClick={() =>
							console.log(formMethods.formState.errors)
						}
					>
						Add your operating hours
					</h2>
					<h3 className="text-[#323539]">
						You can customize this information later.
					</h3>
				</div>
				<div className="flex max-h-[372px] flex-col space-y-3 overflow-scroll px-8">
					{slots.map((slot, i) => (
						<AddLocationTimer
							{...slot}
							key={i}
							index={i}
							slots={slots}
							shouldShowDay
							shouldShowPlus
							setSlots={setSlots as any}
						/>
					))}
				</div>
				<div className="flex flex-col space-y-6 rounded-b-[10px] bg-[#FAFBFC] px-8 py-4">
					<div className="flex flex-col space-y-3">
						<div className="flex w-full flex-col space-y-1.5">
							<div className="flex items-center space-x-4">
								<Label className="max-w-[141px] flex-1 text-base font-medium tracking-[-0.1px] text-[#323539]">
									Est. Wait Time
									<span className="text-[#c9312c]">*</span>
								</Label>
								<div className="flex max-w-[320px] flex-1 items-center space-x-2 rounded-md border border-[#E5E5E7] bg-white px-3 py-2">
									<Input
										{...formMethods.register(
											"queue_settings.estimated_wait_time",
											{
												valueAsNumber: true,
											}
										)}
										inputMode="numeric"
										className="h-[22px] border-none px-0 font-semibold shadow-none"
									/>
									<div className="flex h-[18px] min-w-[62px] items-center border-l border-l-[#B7B7B7] pl-2">
										<p className="rounded-full text-[15px] leading-[22px] text-[#323539]">
											minutes
										</p>
									</div>
								</div>
							</div>
							{formMethods.formState.errors?.queue_settings
								?.estimated_wait_time?.message && (
								<small className="mt-1.5 text-sm text-[#c9312c]">
									{
										formMethods.formState.errors
											?.queue_settings
											?.estimated_wait_time?.message
									}
								</small>
							)}
						</div>

						<div className="flex flex-col space-y-1.5">
							<div className="flex items-center space-x-4">
								<TooltipProvider>
									<Tooltip delayDuration={0}>
										<TooltipTrigger asChild>
											<div className="flex max-w-[141px] flex-1 items-center space-x-2">
												<label className="block flex-1 text-base font-medium tracking-[-0.1px] text-[#323539]">
													Schedule Block
												</label>

												<i className="mgc_information_line schedule-time-block-icon before-text-dark text-[14px]" />
											</div>
										</TooltipTrigger>

										<TooltipContent
											side="top"
											sideOffset={10}
										>
											This is the default time for each
											appointment
										</TooltipContent>
									</Tooltip>
								</TooltipProvider>
								<div className="flex flex-1 flex-col space-y-1.5">
									<div className="flex max-w-[320px] flex-1 items-center justify-between rounded-md border border-[#E5E5E7] bg-white px-3 py-2 text-[#323539]">
										<Select
											value={formMethods
												.watch(
													"queue_settings.schedule_block_in_minutes"
												)
												?.toString()}
											onValueChange={(value) =>
												formMethods.setValue(
													"queue_settings.schedule_block_in_minutes",
													parseInt(value)
												)
											}
										>
											<SelectTrigger className="removeFocus h-3 border-none px-0 pr-1">
												<SelectValue
													placeholder="15"
													className="!w-full text-[#323539]"
												/>
											</SelectTrigger>
											<SelectContent className="!z-[9999]">
												{scheduleBlockOptions.map(
													(option, mainIndex) => {
														return (
															<SelectGroup
																key={mainIndex}
															>
																<SelectItem
																	value={
																		option.value
																	}
																>
																	{
																		option.label
																	}
																</SelectItem>
															</SelectGroup>
														);
													}
												)}
											</SelectContent>
										</Select>
										<div className="flex min-w-[62px] items-center space-x-2">
											<div className="h-[18px] w-[1px] rounded-full bg-[#B7B7B7]" />
											<p className="rounded-full text-[15px] leading-[22px] text-[#323539]">
												minutes
											</p>
										</div>
									</div>
								</div>
							</div>
							{formMethods.formState.errors.queue_settings
								?.schedule_block_in_minutes?.message && (
								<small className="text-sm text-[#c9312c]">
									{
										formMethods.formState.errors
											.queue_settings
											?.schedule_block_in_minutes?.message
									}
								</small>
							)}
						</div>

						<div className="flex w-full flex-col space-y-1.5">
							<div className="flex items-center space-x-4">
								<label
									className="max-w-[141px] flex-1 text-base font-medium tracking-[-0.1px] text-[#323539]"
									onClick={() =>
										setTimezone(
											Intl.DateTimeFormat().resolvedOptions()
												.timeZone
										)
									}
								>
									Time Zone
								</label>
								<div className="flex flex-1 flex-col space-y-1.5">
									<TimeZoneCustomSelect
										value={timezone}
										onChange={(value) => {
											setTimezone(value);
										}}
									/>
									{formMethods.formState.errors
										?.queue_settings?.time_zone
										?.message && (
										<p
											className={`text-sm tracking-[-0.1px] text-[#c9312c]`}
										>
											Kindly select time zone
										</p>
									)}
								</div>
							</div>
							{formMethods.formState?.errors.queue_settings
								?.time_zone?.message && (
								<small className="text-sm text-[#c9312c]">
									{
										formMethods.formState.errors
											.queue_settings.time_zone?.message
									}
								</small>
							)}
						</div>
					</div>
					<div className="flex items-center justify-end space-x-6">
						<LoaderButton
							className="h-10 w-full max-w-[95px] self-end px-0 font-semibold leading-[22px] text-white shadow-[0px_1px_2px_0px_rgba(16,24,40,0.04)] hover:border-transparent"
							loading={NewOnboardingMutation.isPending}
							disabled={NewOnboardingMutation.isPending}
							type="button"
							loaderSize={20}
							onClick={formMethods.handleSubmit((data) => {
								formMethods.setValue(
									"working_hours",
									convertSchedule(slots)
								);
								onSubmitForm(data);
							})}
						>
							Next
						</LoaderButton>
					</div>
				</div>
			</div>
			{onboardingState === 2 && (
				<DefaultOperatingHoursModal setDefaultSlots={setSlots} />
			)}
		</>
	);
};

export default AddOperatingHours;
