name: Azure Static Web Apps CI/CD

on:
    push:
        branches:
            - staging
    pull_request:
        types: [opened, synchronize, reopened, closed]
        branches:
            - staging

jobs:
    build_and_deploy_job:
        if: github.event_name == 'push' || (github.event_name == 'pull_request' && github.event.action != 'closed')
        runs-on: ubuntu-latest

        environment: staging
        env:
            NODE_OPTIONS: --max_old_space_size=4096

        name: Build and Deploy Job
        steps:
            - uses: actions/checkout@v3
              with:
                  submodules: true
                  lfs: false

            - name: Download and Load Environment Variables
              run: |
                  echo "Downloading environment variables..."
                  curl -o .env "${{ secrets.STAGING_AZURE_BLOB_SAS_URL }}"

                  echo "Parsing environment variables..."
                  export $(grep -v '^#' .env | xargs)

                  echo "Environment variables loaded!"

            - name: Build And Deploy
              id: builddeploy
              uses: Azure/static-web-apps-deploy@v1
              with:
                  production_branch: staging
                  azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_KIND_OCEAN_00E30C510 }}
                  repo_token: ${{ secrets.GITHUB_TOKEN }} # Used for Github integrations (i.e. PR comments)
                  action: "upload"
                  app_location: "/" # App source code path
                  api_location: "" # Api source code path - optional
                  output_location: "" # Built app content directory - optional

    close_pull_request_job:
        if: github.event_name == 'pull_request' && github.event.action == 'closed'
        runs-on: ubuntu-latest
        name: Close Pull Request Job
        steps:
            - name: Close Pull Request
              id: closepullrequest
              uses: Azure/static-web-apps-deploy@v1
              with:
                  app_location: "/" # Specify the app source code path
                  azure_static_web_apps_api_token: ${{ secrets.AZURE_STATIC_WEB_APPS_API_TOKEN_KIND_OCEAN_00E30C510 }}
                  action: "close"
