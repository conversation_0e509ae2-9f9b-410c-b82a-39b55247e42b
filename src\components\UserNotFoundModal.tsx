import React from "react";
import { Dialog, DialogContent } from "./ui/dialog";

const UserNotFoundModal: React.FC<{
	show: boolean;
	setShow: React.Dispatch<React.SetStateAction<boolean>>;
	handleUserNotFoundError: () => void;
}> = ({ show, setShow, handleUserNotFoundError }) => {
	return (
		<Dialog open={show} onOpenChange={setShow}>
			<DialogContent className="flex w-[400px] items-center justify-center rounded-lg bg-white p-6 shadow-lg">
				<div className="flex flex-col items-center gap-y-6">
					<img
						className="size-[24px]"
						src="/assets/icons/not_found_icon.png"
						alt="google signup"
					/>
					<p className="mb-4 text-lg font-medium">
						Sorry,we couldn&apos;t find your account
					</p>
					<button
						onClick={handleUserNotFoundError}
						className="rounded bg-primary px-6 py-2 text-sm text-white hover:bg-primary/60"
					>
						Back to Login
					</button>
				</div>
			</DialogContent>
		</Dialog>
	);
};

export default UserNotFoundModal;
